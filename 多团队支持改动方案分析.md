# 标注服务多团队支持改动方案分析

## 一、当前限制原因分析

通过代码分析，当前系统限制每个阶段只能有一个团队的原因主要有以下几点：

### 1. 数据模型设计限制
- `lotphases`表中的`execteam`字段是`VARCHAR(32)`，设计为单一团队ID
- `lotexecutors`表中的`team_uid`字段也是单一值，用于关联执行者与团队
- 数据库索引`idx_lotphases_lot_id_number_execteam`基于单一团队设计

### 2. 业务逻辑限制
- `AssignPhaseExecteam`函数中的逻辑基于单团队设计
- 任务分配逻辑中有明确的团队唯一性检查
- 配额管理机制基于单一团队的配额计算

### 3. 权限控制机制
- 权限授予是按团队级别进行的，但已经支持多个团队
- 任务认领时的权限检查基于单一团队

## 二、最小改动方案

### 方案一：字符串拼接方案（最小改动）

**优点：**
- 改动最小，只需修改数据类型和少量业务逻辑
- 向后兼容性好
- 实现简单快速

**缺点：**
- 数据查询效率较低
- 配额管理复杂
- 扩展性有限

#### 具体修改：

**1. 数据库修改**
```sql
-- 修改lotphases表，将execteam字段改为TEXT类型
ALTER TABLE lotphases ALTER COLUMN execteam TYPE TEXT;

-- 修改唯一索引，移除execteam字段
DROP INDEX idx_lotphases_lot_id_number_execteam;
CREATE INDEX idx_lotphases_lot_id_number ON lotphases (lot_id, number);
```

**2. 代码修改重点**
```go
// internal/biz/lotphase.go
type Lotphase struct {
    // ... 其他字段保持不变 ...
    Execteam      string         `json:"execteam" gorm:"type:text"` // 存储逗号分隔的团队ID
    Quota         ExecteamQuotaW `json:"quota" gorm:"type:jsonb"`   // 存储JSON格式的配额信息
}

// 添加辅助方法
func (p *Lotphase) GetExecteams() []string {
    if p.Execteam == "" {
        return nil
    }
    return strings.Split(p.Execteam, ",")
}

func (p *Lotphase) SetExecteams(teams []string) {
    p.Execteam = strings.Join(teams, ",")
}

// 修改配额结构
type ExecteamQuota struct {
    Quota map[string]*TeamQuota `json:"quota"` // key为team_uid
}

type TeamQuota struct {
    Min     int32 `json:"min"`
    Max     int32 `json:"max"`
    Current int32 `json:"current"`
}
```

### 方案二：关联表方案（推荐）

**优点：**
- 数据结构清晰，查询效率高
- 配额管理精确
- 扩展性好，支持更复杂的团队管理需求
- 符合数据库设计规范

**缺点：**
- 改动相对较大
- 需要数据迁移

#### 具体修改：

**1. 数据库修改**
```sql
-- 创建团队阶段关联表
CREATE TABLE lotphase_teams (
    id BIGSERIAL NOT NULL PRIMARY KEY,
    lotphase_id BIGINT NOT NULL,
    team_uid VARCHAR(32) NOT NULL,
    quota JSONB NOT NULL, -- {min: int, max: int, current: int}
    status VARCHAR(32) NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    FOREIGN KEY (lotphase_id) REFERENCES lotphases(id) ON DELETE CASCADE,
    UNIQUE(lotphase_id, team_uid)
);

-- 创建团队任务分配表
CREATE TABLE team_job_assignments (
    id BIGSERIAL NOT NULL PRIMARY KEY,
    lotphase_team_id BIGINT NOT NULL,
    job_id BIGINT NOT NULL,
    assigned_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status VARCHAR(32) NOT NULL DEFAULT 'assigned',
    FOREIGN KEY (lotphase_team_id) REFERENCES lotphase_teams(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- 移除lotphases表中的execteam和quota字段
ALTER TABLE lotphases DROP COLUMN execteam;
ALTER TABLE lotphases DROP COLUMN quota;
```

**2. 代码修改重点**
```go
// internal/biz/lotphase.go
type LotphaseTeam struct {
    ID         int64     `json:"id"`
    LotphaseID int64     `json:"lotphase_id"`
    TeamUid    string    `json:"team_uid"`
    Quota      TeamQuota `json:"quota"`
    Status     string    `json:"status"`
    CreatedAt  time.Time `json:"created_at"`
    UpdatedAt  time.Time `json:"updated_at"`
}

type TeamQuota struct {
    Min     int32 `json:"min"`
    Max     int32 `json:"max"`
    Current int32 `json:"current"`
}

// 分配团队到阶段
func (b *LotBiz) AssignTeamsToPhase(ctx context.Context, phaseID int64, teams []*TeamAssignment) error {
    return b.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        // 验证阶段存在性
        phase, err := b.repo.GetPhaseByID(ctx, phaseID)
        if err != nil {
            return err
        }

        // 验证团队配额总和不超过100%
        totalQuota := int32(0)
        for _, team := range teams {
            totalQuota += team.Quota.Min
        }
        if totalQuota > 100 {
            return errors.NewErrInvalidField(errors.WithMessage("total team quota exceeds 100%"))
        }

        // 创建或更新团队分配
        for _, team := range teams {
            err := b.repo.UpsertLotphaseTeam(ctx, &LotphaseTeam{
                LotphaseID: phaseID,
                TeamUid:    team.TeamUid,
                Quota: TeamQuota{
                    Min:     team.Quota.Min,
                    Max:     team.Quota.Max,
                    Current: 0,
                },
                Status: "active",
            })
            if err != nil {
                return err
            }
        }

        return nil
    })
}
```

## 三、核心修改点分析

### 1. 任务分配逻辑修改
需要修改以下关键函数：
- `AssignPhaseExecteam`: 支持为一个阶段分配多个团队
- `ClaimJob`: 任务认领时需要选择合适的团队
- `AddPhaseExecutors`: 支持多团队的执行者添加

### 2. 配额管理修改
- 修改配额计算逻辑，支持多团队配额分配
- 实现团队间的配额平衡和调整
- 添加配额监控和报警机制

### 3. 权限控制修改
- `GrantExecteamAccess`函数已经支持多团队，无需修改
- 需要确保任务分配时的权限检查正确

### 4. API接口修改
- `AssignExecteamRequest`需要支持多团队配置
- 前端界面需要支持多团队选择和配额设置

## 四、实施建议

### 阶段一：最小可行方案（1-2周）
1. 采用字符串拼接方案
2. 修改核心业务逻辑支持多团队
3. 保持现有API接口不变，内部逻辑支持多团队

### 阶段二：优化方案（2-3周）
1. 实施关联表方案
2. 优化配额管理和任务分配算法
3. 完善监控和报警机制

### 阶段三：功能增强（1-2周）
1. 添加团队间任务转移功能
2. 实现动态配额调整
3. 完善前端界面和用户体验

## 五、风险评估

### 技术风险：
- 数据迁移风险：需要仔细处理现有数据
- 性能风险：多团队查询可能影响性能
- 兼容性风险：需要确保向后兼容

### 业务风险：
- 配额分配不当可能导致团队间冲突
- 任务分配算法需要仔细设计避免偏向性

## 六、总结

**推荐采用方案二（关联表方案）**，因为：
1. 虽然改动相对较大，但数据结构更清晰
2. 长期维护成本更低
3. 扩展性更好，支持未来更复杂的需求
4. 性能更优，查询效率更高

如果时间紧迫，可以先实施方案一作为过渡，后续再升级到方案二。

## 七、详细实现示例

### 方案一实现示例

#### 1. 权限控制修改
```go
// internal/biz/lot.go
func (o *LotsBiz) GrantExecteamAccess(ctx context.Context, lotID int64, teamUids ...string) error {
    resource := LotScope(kid.StringID(lotID))

    // 为每个团队创建独立的权限策略
    for _, teamUid := range teamUids {
        _, err := client.CreatePolicy(ctx, resource, RoleJobViewer, []string{client.GroupScope(teamUid)})
        if err != nil {
            return err
        }
    }
    return nil
}
```

#### 2. 任务分配逻辑修改
```go
// internal/biz/job.go
func (b *JobsBiz) AssignJobToTeam(ctx context.Context, jobID int64, req *anno.AssignJobRequest) error {
    // 获取任务信息
    job, err := b.repo.GetJobByID(ctx, jobID)
    if err != nil {
        return err
    }

    // 获取阶段信息
    phase, err := b.repo.GetPhase(ctx, job.LotID, job.Phase, "")
    if err != nil {
        return err
    }

    // 获取可用的团队
    availableTeams := phase.GetExecteams()
    if len(availableTeams) == 0 {
        return errors.NewErrFailedPrecondition(errors.WithMessage("no teams available for this phase"))
    }

    // 获取团队配额信息
    quota := phase.Quota.E.Quota
    if quota == nil {
        return errors.NewErrFailedPrecondition(errors.WithMessage("no quota information available"))
    }

    // 选择配额使用最少的团队
    var selectedTeam string
    minUsage := int32(100)
    for _, team := range availableTeams {
        if teamQuota, ok := quota[team]; ok {
            usage := float32(teamQuota.Current) / float32(teamQuota.Max) * 100
            if usage < float32(minUsage) {
                minUsage = int32(usage)
                selectedTeam = team
            }
        }
    }

    if selectedTeam == "" {
        return errors.NewErrFailedPrecondition(errors.WithMessage("no team available with quota"))
    }

    // 更新任务分配
    job.ExecutorUid = req.ExecutorUid
    job.Execteam = selectedTeam
    job.State = JobStateAssigned

    return b.repo.UpdateJob(ctx, job, field.NewMask("executor_uid", "execteam", "state"))
}
```

#### 3. 阶段团队分配修改
```go
// internal/biz/lotphase.go
func (o *LotsBiz) AssignPhaseExecteam(ctx context.Context, req *anno.AssignExecteamRequest) error {
    lotID := kid.ParseID(req.Uid)
    lot, err := o.repo.GetByID(ctx, lotID, false)
    if err != nil {
        return err
    }

    return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        // 处理每个阶段的团队分配
        for _, reqPhase := range req.Phases {
            phaseNum := reqPhase.Phase

            // 获取现有阶段
            phase, err := o.repo.GetPhase(ctx, lotID, phaseNum, "")
            if err != nil {
                return err
            }

            // 构建团队列表和配额信息
            teams := make([]string, 0, len(reqPhase.Execteams))
            quotaMap := make(map[string]*TeamQuota)

            for _, execteam := range reqPhase.Execteams {
                teams = append(teams, execteam.Execteam)
                quotaMap[execteam.Execteam] = &TeamQuota{
                    Min: execteam.Quota.Min,
                    Max: execteam.Quota.Max,
                }
            }

            // 更新阶段信息
            phase.SetExecteams(teams)
            phase.Quota = ExecteamQuotaW{
                E: &ExecteamQuota{
                    Quota: quotaMap,
                },
            }

            // 保存更新
            err = o.repo.UpdatePhase(ctx, phase, field.NewMask("execteam", "quota"))
            if err != nil {
                return err
            }
        }

        // 授予权限
        allTeams := make([]string, 0)
        for _, reqPhase := range req.Phases {
            for _, execteam := range reqPhase.Execteams {
                allTeams = append(allTeams, execteam.Execteam)
            }
        }

        if len(allTeams) > 0 {
            return o.GrantExecteamAccess(ctx, lotID, allTeams...)
        }

        return nil
    })
}
```

### 方案二实现示例

#### 1. 数据访问层修改
```go
// internal/data/lotphase.go
func (r *lotsRepo) UpsertLotphaseTeam(ctx context.Context, team *biz.LotphaseTeam) error {
    return r.data.WithCtx(ctx).
        Clauses(clause.OnConflict{
            Columns:   []clause.Column{{Name: "lotphase_id"}, {Name: "team_uid"}},
            DoUpdates: clause.AssignmentColumns([]string{"quota", "status", "updated_at"}),
        }).
        Create(team).Error
}

func (r *lotsRepo) GetLotphaseTeams(ctx context.Context, lotphaseID int64) ([]*biz.LotphaseTeam, error) {
    var teams []*biz.LotphaseTeam
    err := r.data.WithCtx(ctx).
        Where("lotphase_id = ? AND status = ?", lotphaseID, "active").
        Find(&teams).Error
    return teams, err
}

func (r *lotsRepo) GetTeamQuotaUsage(ctx context.Context, lotphaseTeamID int64) (int32, error) {
    var count int64
    err := r.data.WithCtx(ctx).Model(&biz.TeamJobAssignment{}).
        Where("lotphase_team_id = ? AND status = ?", lotphaseTeamID, "assigned").
        Count(&count).Error
    return int32(count), err
}
```

#### 2. 智能任务分配算法
```go
// internal/biz/job.go
func (b *JobsBiz) SelectOptimalTeam(ctx context.Context, phaseID int64) (string, error) {
    // 获取阶段的所有团队
    teams, err := b.repo.GetLotphaseTeams(ctx, phaseID)
    if err != nil {
        return "", err
    }

    if len(teams) == 0 {
        return "", errors.NewErrFailedPrecondition(errors.WithMessage("no teams available"))
    }

    // 计算每个团队的负载情况
    type teamLoad struct {
        TeamUid     string
        Usage       float32
        Available   bool
    }

    teamLoads := make([]teamLoad, 0, len(teams))

    for _, team := range teams {
        current, err := b.repo.GetTeamQuotaUsage(ctx, team.ID)
        if err != nil {
            continue
        }

        usage := float32(current) / float32(team.Quota.Max) * 100
        available := current < team.Quota.Max

        teamLoads = append(teamLoads, teamLoad{
            TeamUid:   team.TeamUid,
            Usage:     usage,
            Available: available,
        })
    }

    // 选择负载最低且有可用配额的团队
    var selectedTeam string
    minUsage := float32(100)

    for _, load := range teamLoads {
        if load.Available && load.Usage < minUsage {
            minUsage = load.Usage
            selectedTeam = load.TeamUid
        }
    }

    if selectedTeam == "" {
        return "", errors.NewErrResourceExhausted(errors.WithMessage("all teams quota exceeded"))
    }

    return selectedTeam, nil
}
```

## 八、迁移策略

### 数据迁移脚本（方案一到方案二）
```sql
-- 1. 创建新表
CREATE TABLE lotphase_teams (
    id BIGSERIAL NOT NULL PRIMARY KEY,
    lotphase_id BIGINT NOT NULL,
    team_uid VARCHAR(32) NOT NULL,
    quota JSONB NOT NULL,
    status VARCHAR(32) NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    FOREIGN KEY (lotphase_id) REFERENCES lotphases(id) ON DELETE CASCADE,
    UNIQUE(lotphase_id, team_uid)
);

-- 2. 迁移现有数据
INSERT INTO lotphase_teams (lotphase_id, team_uid, quota, created_at, updated_at)
SELECT
    id as lotphase_id,
    unnest(string_to_array(execteam, ',')) as team_uid,
    quota,
    created_at,
    updated_at
FROM lotphases
WHERE execteam IS NOT NULL AND execteam != '';

-- 3. 验证数据迁移
SELECT
    lp.id,
    lp.execteam,
    array_agg(lpt.team_uid) as migrated_teams
FROM lotphases lp
LEFT JOIN lotphase_teams lpt ON lp.id = lpt.lotphase_id
WHERE lp.execteam IS NOT NULL AND lp.execteam != ''
GROUP BY lp.id, lp.execteam;

-- 4. 删除旧字段（确认迁移成功后执行）
-- ALTER TABLE lotphases DROP COLUMN execteam;
-- ALTER TABLE lotphases DROP COLUMN quota;
```

## 九、测试策略

### 1. 单元测试
- 团队分配逻辑测试
- 配额计算测试
- 任务分配算法测试

### 2. 集成测试
- 多团队协作流程测试
- 权限控制测试
- 数据一致性测试

### 3. 性能测试
- 大量团队场景下的查询性能
- 并发任务分配性能
- 配额更新性能

### 4. 回归测试
- 现有单团队功能不受影响
- API接口兼容性测试
- 数据迁移正确性验证

## 十、监控和运维

### 1. 关键指标监控
- 各团队任务分配比例
- 配额使用率
- 任务分配延迟
- 团队间负载均衡度

### 2. 告警设置
- 团队配额超限告警
- 任务分配失败告警
- 负载不均衡告警

### 3. 运维工具
- 团队配额调整工具
- 任务重新分配工具
- 负载均衡调整工具
