# 标注服务多团队支持改动方案分析

## 一、当前限制原因分析

通过代码分析，当前系统限制每个阶段只能有一个团队的原因主要有以下几点：

### 1. 数据模型设计限制
- `lotphases`表中的`execteam`字段是`VARCHAR(32)`，设计为单一团队ID
- `lotexecutors`表中的`team_uid`字段也是单一值，用于关联执行者与团队
- 数据库索引`idx_lotphases_lot_id_number_execteam`基于单一团队设计

### 2. 业务逻辑限制
- `AssignPhaseExecteam`函数中的逻辑基于单团队设计
- 任务分配逻辑中有明确的团队唯一性检查
- 配额管理机制基于单一团队的配额计算

### 3. 权限控制机制
- 权限授予是按团队级别进行的，但已经支持多个团队
- 任务认领时的权限检查基于单一团队

## 二、最小改动方案

### 方案一：字符串拼接方案（最小改动）

**优点：**
- 改动最小，只需修改数据类型和少量业务逻辑
- 向后兼容性好
- 实现简单快速

**缺点：**
- 数据查询效率较低
- 配额管理复杂
- 扩展性有限

#### 具体修改：

**1. 数据库修改**
```sql
-- 修改lotphases表，将execteam字段改为TEXT类型
ALTER TABLE lotphases ALTER COLUMN execteam TYPE TEXT;

-- 修改唯一索引，移除execteam字段
DROP INDEX idx_lotphases_lot_id_number_execteam;
CREATE INDEX idx_lotphases_lot_id_number ON lotphases (lot_id, number);
```

**2. 代码修改重点**
```go
// internal/biz/lotphase.go
type Lotphase struct {
    // ... 其他字段保持不变 ...
    Execteam      string         `json:"execteam" gorm:"type:text"` // 存储逗号分隔的团队ID
    Quota         ExecteamQuotaW `json:"quota" gorm:"type:jsonb"`   // 存储JSON格式的配额信息
}

// 添加辅助方法
func (p *Lotphase) GetExecteams() []string {
    if p.Execteam == "" {
        return nil
    }
    return strings.Split(p.Execteam, ",")
}

func (p *Lotphase) SetExecteams(teams []string) {
    p.Execteam = strings.Join(teams, ",")
}

// 修改配额结构
type ExecteamQuota struct {
    Quota map[string]*TeamQuota `json:"quota"` // key为team_uid
}

type TeamQuota struct {
    Min     int32 `json:"min"`
    Max     int32 `json:"max"`
    Current int32 `json:"current"`
}
```

### 方案二：关联表方案（推荐）

**优点：**
- 数据结构清晰，查询效率高
- 配额管理精确
- 扩展性好，支持更复杂的团队管理需求
- 符合数据库设计规范

**缺点：**
- 改动相对较大
- 需要数据迁移

#### 具体修改：

**1. 数据库修改**
```sql
-- 创建团队阶段关联表
CREATE TABLE lotphase_teams (
    id BIGSERIAL NOT NULL PRIMARY KEY,
    lotphase_id BIGINT NOT NULL,
    team_uid VARCHAR(32) NOT NULL,
    quota JSONB NOT NULL, -- {min: int, max: int, current: int}
    status VARCHAR(32) NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    FOREIGN KEY (lotphase_id) REFERENCES lotphases(id) ON DELETE CASCADE,
    UNIQUE(lotphase_id, team_uid)
);

-- 创建团队任务分配表
CREATE TABLE team_job_assignments (
    id BIGSERIAL NOT NULL PRIMARY KEY,
    lotphase_team_id BIGINT NOT NULL,
    job_id BIGINT NOT NULL,
    assigned_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status VARCHAR(32) NOT NULL DEFAULT 'assigned',
    FOREIGN KEY (lotphase_team_id) REFERENCES lotphase_teams(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- 移除lotphases表中的execteam和quota字段
ALTER TABLE lotphases DROP COLUMN execteam;
ALTER TABLE lotphases DROP COLUMN quota;
```

**2. 代码修改重点**
```go
// internal/biz/lotphase.go
type LotphaseTeam struct {
    ID         int64     `json:"id"`
    LotphaseID int64     `json:"lotphase_id"`
    TeamUid    string    `json:"team_uid"`
    Quota      TeamQuota `json:"quota"`
    Status     string    `json:"status"`
    CreatedAt  time.Time `json:"created_at"`
    UpdatedAt  time.Time `json:"updated_at"`
}

type TeamQuota struct {
    Min     int32 `json:"min"`
    Max     int32 `json:"max"`
    Current int32 `json:"current"`
}

// 分配团队到阶段
func (b *LotBiz) AssignTeamsToPhase(ctx context.Context, phaseID int64, teams []*TeamAssignment) error {
    return b.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        // 验证阶段存在性
        phase, err := b.repo.GetPhaseByID(ctx, phaseID)
        if err != nil {
            return err
        }

        // 验证团队配额总和不超过100%
        totalQuota := int32(0)
        for _, team := range teams {
            totalQuota += team.Quota.Min
        }
        if totalQuota > 100 {
            return errors.NewErrInvalidField(errors.WithMessage("total team quota exceeds 100%"))
        }

        // 创建或更新团队分配
        for _, team := range teams {
            err := b.repo.UpsertLotphaseTeam(ctx, &LotphaseTeam{
                LotphaseID: phaseID,
                TeamUid:    team.TeamUid,
                Quota: TeamQuota{
                    Min:     team.Quota.Min,
                    Max:     team.Quota.Max,
                    Current: 0,
                },
                Status: "active",
            })
            if err != nil {
                return err
            }
        }

        return nil
    })
}
```

## 三、核心修改点分析

### 1. 任务分配逻辑修改
需要修改以下关键函数：
- `AssignPhaseExecteam`: 支持为一个阶段分配多个团队
- `ClaimJob`: 任务认领时需要选择合适的团队
- `AddPhaseExecutors`: 支持多团队的执行者添加

### 2. 配额管理修改
- 修改配额计算逻辑，支持多团队配额分配
- 实现团队间的配额平衡和调整
- 添加配额监控和报警机制

### 3. 权限控制修改
- `GrantExecteamAccess`函数已经支持多团队，无需修改
- 需要确保任务分配时的权限检查正确

### 4. API接口修改
- `AssignExecteamRequest`需要支持多团队配置
- 前端界面需要支持多团队选择和配额设置

## 四、实施建议

### 阶段一：最小可行方案（1-2周）
1. 采用字符串拼接方案
2. 修改核心业务逻辑支持多团队
3. 保持现有API接口不变，内部逻辑支持多团队

### 阶段二：优化方案（2-3周）
1. 实施关联表方案
2. 优化配额管理和任务分配算法
3. 完善监控和报警机制

### 阶段三：功能增强（1-2周）
1. 添加团队间任务转移功能
2. 实现动态配额调整
3. 完善前端界面和用户体验

## 五、风险评估

### 技术风险：
- 数据迁移风险：需要仔细处理现有数据
- 性能风险：多团队查询可能影响性能
- 兼容性风险：需要确保向后兼容

### 业务风险：
- 配额分配不当可能导致团队间冲突
- 任务分配算法需要仔细设计避免偏向性

## 六、总结

**推荐采用方案二（关联表方案）**，因为：
1. 虽然改动相对较大，但数据结构更清晰
2. 长期维护成本更低
3. 扩展性更好，支持未来更复杂的需求
4. 性能更优，查询效率更高

如果时间紧迫，可以先实施方案一作为过渡，后续再升级到方案二。

## 七、权重概念详细说明

### 基于任务认领（ClaimJob）的权重分配机制

您的理解完全正确！权重概念确实是基于最终团队成员领取任务（ClaimJob操作）的实际情况设计的。

#### 1. **当前ClaimJob流程分析**
```go
// 当团队成员执行ClaimJob时的核心逻辑
func (o *JobsBiz) tryClaim(ctx context.Context, lotID int64, subtype string, executorUid, execteamUid string, prefer ClaimJobPrefer) (*Job, error) {
    // 1. 首先检查是否有已分配的进行中任务
    candi, err := o.Repo.GetAssignedJob(ctx, lotID, subtype, executorUid, prefer)

    // 2. 如果没有，尝试找到未分配和未认领的任务
    if candi == nil {
        jobs, err = o.Repo.GetJobsToAssign(ctx, lotID, subtype, executorUid)
    }

    // 3. 关键：检查团队配额限制
    for _, candi := range jobs {
        quota, err := o.lotrepo.GetExecteamQuota(ctx, candi.LotID, executorUid)
        if !quota.NoLimit() {
            if quota.ClaimedJobs >= quota.MaxJobs {
                continue // 跳过配额已满的任务
            }
        }

        // 4. 执行任务认领，更新团队配额
        ok, err := o.ChangeJobState(ctx, candi, &JobAction{
            Action: JobActionClaim,
            NewState: JobStateTransition{
                Execteam:    execteamUid,  // 分配给具体团队
                ExecutorUid: executorUid,
            },
        }, true)
    }
}
```

#### 2. **多团队权重分配的核心逻辑**
在多团队支持中，权重分配应该在任务认领时动态决定：

```go
// 智能团队选择算法（基于权重/负载均衡）
func (b *JobsBiz) SelectOptimalTeamForClaim(ctx context.Context, phaseID int64, executorUid string) (string, error) {
    // 获取该阶段的所有可用团队
    teams, err := b.repo.GetLotphaseTeams(ctx, phaseID)
    if err != nil {
        return "", err
    }

    // 检查执行者属于哪些团队
    userTeams, err := b.repo.GetUserTeams(ctx, executorUid)
    if err != nil {
        return "", err
    }

    // 计算每个团队的当前负载和可用性
    type teamScore struct {
        TeamUid     string
        LoadRatio   float32  // 当前负载比例
        Available   bool     // 是否还有配额
        Priority    int      // 优先级（用户所属团队优先级更高）
    }

    var candidates []teamScore
    for _, team := range teams {
        // 检查用户是否属于该团队
        priority := 1
        if contains(userTeams, team.TeamUid) {
            priority = 10 // 用户所属团队优先级更高
        }

        // 计算负载比例
        loadRatio := float32(team.Quota.Current) / float32(team.Quota.Max)
        available := team.Quota.Current < team.Quota.Max

        candidates = append(candidates, teamScore{
            TeamUid:   team.TeamUid,
            LoadRatio: loadRatio,
            Available: available,
            Priority:  priority,
        })
    }

    // 选择最优团队：优先级高 + 负载低 + 有配额
    sort.Slice(candidates, func(i, j int) bool {
        a, b := candidates[i], candidates[j]
        if a.Available != b.Available {
            return a.Available // 有配额的优先
        }
        if a.Priority != b.Priority {
            return a.Priority > b.Priority // 优先级高的优先
        }
        return a.LoadRatio < b.LoadRatio // 负载低的优先
    })

    if len(candidates) > 0 && candidates[0].Available {
        return candidates[0].TeamUid, nil
    }

    return "", errors.NewErrResourceExhausted(errors.WithMessage("no team available"))
}
```

#### 3. **权重分配的实际应用场景**

**场景1：用户主动认领任务**
```go
// 用户点击"认领任务"时
func (o *JobsBiz) ClaimJobWithTeamSelection(ctx context.Context, req *anno.ClaimJobRequest) (*Job, error) {
    user := UserFromCtx(ctx)

    // 1. 找到可认领的任务
    jobs, err := o.Repo.GetJobsToAssign(ctx, lotID, subtype, user.GetUid())

    for _, job := range jobs {
        // 2. 为该任务选择最优团队
        selectedTeam, err := o.SelectOptimalTeamForClaim(ctx, job.LotPhase.ID, user.GetUid())
        if err != nil {
            continue
        }

        // 3. 执行认领，分配给选定的团队
        ok, err := o.ChangeJobState(ctx, job, &JobAction{
            Action: JobActionClaim,
            NewState: JobStateTransition{
                Execteam:    selectedTeam,
                ExecutorUid: user.GetUid(),
            },
        }, true)

        if ok {
            return job, nil
        }
    }
}
```

**场景2：管理员预分配任务**
```go
// 管理员可以预先将任务分配给特定团队
func (o *JobsBiz) PreAssignJobsToTeams(ctx context.Context, phaseID int64, strategy string) error {
    jobs, err := o.Repo.GetUnassignedJobs(ctx, phaseID)
    teams, err := o.repo.GetLotphaseTeams(ctx, phaseID)

    switch strategy {
    case "round_robin":
        // 轮询分配
        for i, job := range jobs {
            teamIndex := i % len(teams)
            selectedTeam := teams[teamIndex].TeamUid
            job.PreAssignedTeam = selectedTeam
        }
    case "load_balance":
        // 负载均衡分配
        for _, job := range jobs {
            selectedTeam := selectLeastLoadedTeam(teams)
            job.PreAssignedTeam = selectedTeam
        }
    case "quota_proportional":
        // 按配额比例分配
        totalQuota := calculateTotalQuota(teams)
        for _, job := range jobs {
            selectedTeam := selectTeamByQuotaProportion(teams, totalQuota)
            job.PreAssignedTeam = selectedTeam
        }
    }
}
```

#### 4. **权重配置的灵活性**

**配额权重配置示例：**
```json
{
  "phase_teams": [
    {
      "team_uid": "team_a",
      "quota": {
        "min": 30,    // 最少分配30%的任务
        "max": 50,    // 最多分配50%的任务
        "weight": 1.5 // 权重系数，影响任务分配优先级
      }
    },
    {
      "team_uid": "team_b",
      "quota": {
        "min": 20,
        "max": 40,
        "weight": 1.0
      }
    },
    {
      "team_uid": "team_c",
      "quota": {
        "min": 10,
        "max": 30,
        "weight": 0.8
      }
    }
  ]
}
```

**动态权重调整：**
```go
// 根据团队表现动态调整权重
func (b *JobsBiz) AdjustTeamWeights(ctx context.Context, phaseID int64) error {
    teams, err := b.repo.GetLotphaseTeams(ctx, phaseID)

    for _, team := range teams {
        // 获取团队绩效指标
        performance, err := b.repo.GetTeamPerformance(ctx, team.ID)
        if err != nil {
            continue
        }

        // 根据绩效调整权重
        newWeight := calculateWeightByPerformance(performance)
        team.Quota.Weight = newWeight

        // 更新数据库
        err = b.repo.UpdateLotphaseTeam(ctx, team)
    }
}

func calculateWeightByPerformance(perf *TeamPerformance) float32 {
    // 综合考虑：完成率、质量、速度等因素
    completionRate := perf.CompletedJobs / perf.AssignedJobs
    qualityScore := perf.AverageQualityScore
    speedScore := perf.AverageCompletionTime

    weight := completionRate * 0.4 + qualityScore * 0.4 + speedScore * 0.2
    return weight
}
```

## 九、详细实现示例

### 方案一实现示例

#### 1. 权限控制修改
```go
// internal/biz/lot.go
func (o *LotsBiz) GrantExecteamAccess(ctx context.Context, lotID int64, teamUids ...string) error {
    resource := LotScope(kid.StringID(lotID))

    // 为每个团队创建独立的权限策略
    for _, teamUid := range teamUids {
        _, err := client.CreatePolicy(ctx, resource, RoleJobViewer, []string{client.GroupScope(teamUid)})
        if err != nil {
            return err
        }
    }
    return nil
}
```

#### 2. 任务分配逻辑修改
```go
// internal/biz/job.go
func (b *JobsBiz) AssignJobToTeam(ctx context.Context, jobID int64, req *anno.AssignJobRequest) error {
    // 获取任务信息
    job, err := b.repo.GetJobByID(ctx, jobID)
    if err != nil {
        return err
    }

    // 获取阶段信息
    phase, err := b.repo.GetPhase(ctx, job.LotID, job.Phase, "")
    if err != nil {
        return err
    }

    // 获取可用的团队
    availableTeams := phase.GetExecteams()
    if len(availableTeams) == 0 {
        return errors.NewErrFailedPrecondition(errors.WithMessage("no teams available for this phase"))
    }

    // 获取团队配额信息
    quota := phase.Quota.E.Quota
    if quota == nil {
        return errors.NewErrFailedPrecondition(errors.WithMessage("no quota information available"))
    }

    // 选择配额使用最少的团队
    var selectedTeam string
    minUsage := int32(100)
    for _, team := range availableTeams {
        if teamQuota, ok := quota[team]; ok {
            usage := float32(teamQuota.Current) / float32(teamQuota.Max) * 100
            if usage < float32(minUsage) {
                minUsage = int32(usage)
                selectedTeam = team
            }
        }
    }

    if selectedTeam == "" {
        return errors.NewErrFailedPrecondition(errors.WithMessage("no team available with quota"))
    }

    // 更新任务分配
    job.ExecutorUid = req.ExecutorUid
    job.Execteam = selectedTeam
    job.State = JobStateAssigned

    return b.repo.UpdateJob(ctx, job, field.NewMask("executor_uid", "execteam", "state"))
}
```

#### 3. 阶段团队分配修改
```go
// internal/biz/lotphase.go
func (o *LotsBiz) AssignPhaseExecteam(ctx context.Context, req *anno.AssignExecteamRequest) error {
    lotID := kid.ParseID(req.Uid)
    lot, err := o.repo.GetByID(ctx, lotID, false)
    if err != nil {
        return err
    }

    return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        // 处理每个阶段的团队分配
        for _, reqPhase := range req.Phases {
            phaseNum := reqPhase.Phase

            // 获取现有阶段
            phase, err := o.repo.GetPhase(ctx, lotID, phaseNum, "")
            if err != nil {
                return err
            }

            // 构建团队列表和配额信息
            teams := make([]string, 0, len(reqPhase.Execteams))
            quotaMap := make(map[string]*TeamQuota)

            for _, execteam := range reqPhase.Execteams {
                teams = append(teams, execteam.Execteam)
                quotaMap[execteam.Execteam] = &TeamQuota{
                    Min: execteam.Quota.Min,
                    Max: execteam.Quota.Max,
                }
            }

            // 更新阶段信息
            phase.SetExecteams(teams)
            phase.Quota = ExecteamQuotaW{
                E: &ExecteamQuota{
                    Quota: quotaMap,
                },
            }

            // 保存更新
            err = o.repo.UpdatePhase(ctx, phase, field.NewMask("execteam", "quota"))
            if err != nil {
                return err
            }
        }

        // 授予权限
        allTeams := make([]string, 0)
        for _, reqPhase := range req.Phases {
            for _, execteam := range reqPhase.Execteams {
                allTeams = append(allTeams, execteam.Execteam)
            }
        }

        if len(allTeams) > 0 {
            return o.GrantExecteamAccess(ctx, lotID, allTeams...)
        }

        return nil
    })
}
```

### 方案二实现示例

#### 1. 数据访问层修改
```go
// internal/data/lotphase.go
func (r *lotsRepo) UpsertLotphaseTeam(ctx context.Context, team *biz.LotphaseTeam) error {
    return r.data.WithCtx(ctx).
        Clauses(clause.OnConflict{
            Columns:   []clause.Column{{Name: "lotphase_id"}, {Name: "team_uid"}},
            DoUpdates: clause.AssignmentColumns([]string{"quota", "status", "updated_at"}),
        }).
        Create(team).Error
}

func (r *lotsRepo) GetLotphaseTeams(ctx context.Context, lotphaseID int64) ([]*biz.LotphaseTeam, error) {
    var teams []*biz.LotphaseTeam
    err := r.data.WithCtx(ctx).
        Where("lotphase_id = ? AND status = ?", lotphaseID, "active").
        Find(&teams).Error
    return teams, err
}

func (r *lotsRepo) GetTeamQuotaUsage(ctx context.Context, lotphaseTeamID int64) (int32, error) {
    var count int64
    err := r.data.WithCtx(ctx).Model(&biz.TeamJobAssignment{}).
        Where("lotphase_team_id = ? AND status = ?", lotphaseTeamID, "assigned").
        Count(&count).Error
    return int32(count), err
}
```

#### 2. 智能任务分配算法
```go
// internal/biz/job.go
func (b *JobsBiz) SelectOptimalTeam(ctx context.Context, phaseID int64) (string, error) {
    // 获取阶段的所有团队
    teams, err := b.repo.GetLotphaseTeams(ctx, phaseID)
    if err != nil {
        return "", err
    }

    if len(teams) == 0 {
        return "", errors.NewErrFailedPrecondition(errors.WithMessage("no teams available"))
    }

    // 计算每个团队的负载情况
    type teamLoad struct {
        TeamUid     string
        Usage       float32
        Available   bool
    }

    teamLoads := make([]teamLoad, 0, len(teams))

    for _, team := range teams {
        current, err := b.repo.GetTeamQuotaUsage(ctx, team.ID)
        if err != nil {
            continue
        }

        usage := float32(current) / float32(team.Quota.Max) * 100
        available := current < team.Quota.Max

        teamLoads = append(teamLoads, teamLoad{
            TeamUid:   team.TeamUid,
            Usage:     usage,
            Available: available,
        })
    }

    // 选择负载最低且有可用配额的团队
    var selectedTeam string
    minUsage := float32(100)

    for _, load := range teamLoads {
        if load.Available && load.Usage < minUsage {
            minUsage = load.Usage
            selectedTeam = load.TeamUid
        }
    }

    if selectedTeam == "" {
        return "", errors.NewErrResourceExhausted(errors.WithMessage("all teams quota exceeded"))
    }

    return selectedTeam, nil
}
```

## 八、迁移策略

### 数据迁移脚本（方案一到方案二）
```sql
-- 1. 创建新表
CREATE TABLE lotphase_teams (
    id BIGSERIAL NOT NULL PRIMARY KEY,
    lotphase_id BIGINT NOT NULL,
    team_uid VARCHAR(32) NOT NULL,
    quota JSONB NOT NULL,
    status VARCHAR(32) NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    FOREIGN KEY (lotphase_id) REFERENCES lotphases(id) ON DELETE CASCADE,
    UNIQUE(lotphase_id, team_uid)
);

-- 2. 迁移现有数据
INSERT INTO lotphase_teams (lotphase_id, team_uid, quota, created_at, updated_at)
SELECT
    id as lotphase_id,
    unnest(string_to_array(execteam, ',')) as team_uid,
    quota,
    created_at,
    updated_at
FROM lotphases
WHERE execteam IS NOT NULL AND execteam != '';

-- 3. 验证数据迁移
SELECT
    lp.id,
    lp.execteam,
    array_agg(lpt.team_uid) as migrated_teams
FROM lotphases lp
LEFT JOIN lotphase_teams lpt ON lp.id = lpt.lotphase_id
WHERE lp.execteam IS NOT NULL AND lp.execteam != ''
GROUP BY lp.id, lp.execteam;

-- 4. 删除旧字段（确认迁移成功后执行）
-- ALTER TABLE lotphases DROP COLUMN execteam;
-- ALTER TABLE lotphases DROP COLUMN quota;
```

## 九、测试策略

### 1. 单元测试
- 团队分配逻辑测试
- 配额计算测试
- 任务分配算法测试

### 2. 集成测试
- 多团队协作流程测试
- 权限控制测试
- 数据一致性测试

### 3. 性能测试
- 大量团队场景下的查询性能
- 并发任务分配性能
- 配额更新性能

### 4. 回归测试
- 现有单团队功能不受影响
- API接口兼容性测试
- 数据迁移正确性验证

## 十、团队隔离机制详细分析

### 1. **当前系统的团队隔离机制**

您的理解完全正确！当前系统已经有完善的团队隔离机制，各个团队是严格隔离的，不会出现跨团队问题。

#### 1.1 权限层面的隔离
```go
// 权限检查确保团队隔离
func (o *LotsService) ListLotsByExecutor(ctx context.Context, req *anno.ListLotsByExecutorRequest) {
    switch {
    case req.TeamUid != "":
        // 检查用户是否有该团队的管理权限
        if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsGroup, req.TeamUid) {
            return nil, errors.NewErrForbidden()
        }
        filter.ExecteamUids = []string{req.TeamUid}
    default:
        // 默认只能看到自己的任务
        filter.UserUid = op.GetUid()
    }
}
```

#### 1.2 数据查询层面的隔离
```go
// 任务查询时的团队隔离
func (o *jobsRepo) GetJobExecutorTeam(ctx context.Context, jobID int64, executor string) (execteam string, err error) {
    // 只能查询到执行者所属团队的任务
    err = o.data.WithCtx(ctx).Model(&biz.Lotexecutor{}).
        Joins("JOIN jobs ON jobs.lot_id = lotexecutors.lot_id").
        Where("jobs.id = ?", jobID).
        Where("lotexecutors.user_uid = ?", executor).
        Select("lotexecutors.team_uid").
        First(&execteam).Error
}
```

#### 1.3 任务分配层面的隔离
```go
// 任务认领时的团队验证
func (o *JobsBiz) tryClaim(ctx context.Context, lotID int64, subtype string, executorUid, execteamUid string, prefer ClaimJobPrefer) (*Job, error) {
    // 检查团队配额，确保只能认领本团队配额内的任务
    quota, err := o.lotrepo.GetExecteamQuota(ctx, candi.LotID, executorUid)
    if !quota.NoLimit() {
        if quota.ClaimedJobs >= quota.MaxJobs {
            continue // 团队配额已满，无法认领
        }
    }

    // 任务状态变更时记录团队信息
    ok, err := o.ChangeJobState(ctx, candi, &JobAction{
        Action: JobActionClaim,
        NewState: JobStateTransition{
            Execteam:    execteamUid, // 明确标记任务属于哪个团队
            ExecutorUid: executorUid,
        },
    }, true)
}
```

### 2. **多团队支持中的隔离保障**

#### 2.1 数据隔离保障
```go
// 方案二：关联表方案中的隔离机制
func (b *JobBiz) CheckTeamJobAccess(ctx context.Context, jobID int64, teamUid string) error {
    // 获取任务信息
    job, err := b.jobRepo.GetJobByID(ctx, jobID)
    if err != nil {
        return err
    }

    // 检查团队是否被分配到该阶段
    phaseTeam, err := b.lotRepo.GetLotphaseTeam(ctx, job.LotID, job.Phase, teamUid)
    if err != nil {
        return errors.NewErrForbidden(errors.WithMessage("team not assigned to this phase"))
    }

    // 检查团队状态
    if phaseTeam.Status != "active" {
        return errors.NewErrForbidden(errors.WithMessage("team is not active in this phase"))
    }

    return nil
}
```

#### 2.2 任务分配隔离
```go
// 智能团队选择时的隔离验证
func (b *JobsBiz) SelectOptimalTeamForClaim(ctx context.Context, phaseID int64, executorUid string) (string, error) {
    // 1. 获取用户所属的团队
    userTeams, err := b.repo.GetUserTeams(ctx, executorUid)
    if err != nil {
        return "", err
    }

    // 2. 获取该阶段的可用团队
    phaseTeams, err := b.repo.GetLotphaseTeams(ctx, phaseID)
    if err != nil {
        return "", err
    }

    // 3. 只考虑用户所属的团队（隔离保障）
    var availableTeams []LotphaseTeam
    for _, phaseTeam := range phaseTeams {
        if contains(userTeams, phaseTeam.TeamUid) {
            availableTeams = append(availableTeams, phaseTeam)
        }
    }

    if len(availableTeams) == 0 {
        return "", errors.NewErrForbidden(errors.WithMessage("user not assigned to any team in this phase"))
    }

    // 4. 在用户所属团队中选择最优的
    return selectBestTeam(availableTeams), nil
}
```

#### 2.3 API接口层面的隔离
```go
// 任务分配接口的权限检查
func (o *JobsService) AssignJob(ctx context.Context, req *anno.AssignJobRequest) (*emptypb.Empty, error) {
    job, err := o.bz.GetByUid(ctx, req.Uid, true)
    if err != nil {
        return nil, err
    }

    // 关键：检查是否有该团队的管理权限
    if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsGroup, job.LotPhase.Execteam) {
        return nil, errors.NewErrForbidden()
    }

    return &emptypb.Empty{}, o.bz.AssignJob(ctx, job, req)
}
```

### 3. **隔离机制的多层保障**

#### 3.1 数据库层面
- `lotexecutors`表通过`team_uid`字段确保用户只能属于特定团队
- `jobs`表通过`execteam`字段明确任务归属
- 数据库索引和外键约束保证数据一致性

#### 3.2 业务逻辑层面
- 任务认领时验证用户团队身份
- 配额检查确保团队不会超额认领
- 状态变更时记录团队信息

#### 3.3 权限控制层面
- IAM系统的团队权限控制
- API接口的权限验证
- 资源访问的范围限制

### 4. **多团队场景下的隔离增强**

#### 4.1 任务可见性隔离
```go
// 确保团队只能看到分配给自己的任务
func (b *JobsBiz) GetTeamVisibleJobs(ctx context.Context, teamUid string, phaseID int64) ([]*Job, error) {
    // 验证团队是否被分配到该阶段
    _, err := b.repo.GetLotphaseTeam(ctx, phaseID, teamUid)
    if err != nil {
        return nil, errors.NewErrForbidden(errors.WithMessage("team not assigned to this phase"))
    }

    // 只返回该团队可见的任务
    return b.repo.GetJobsByTeam(ctx, phaseID, teamUid)
}
```

#### 4.2 配额隔离
```go
// 团队配额独立计算，互不影响
func (b *JobsBiz) UpdateTeamQuota(ctx context.Context, lotphaseTeamID int64, increment int) error {
    return b.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        // 只更新特定团队的配额，不影响其他团队
        err := b.repo.UpdateLotphaseTeamQuota(ctx, lotphaseTeamID, increment)
        if err != nil {
            return err
        }

        // 验证配额不会超限
        team, err := b.repo.GetLotphaseTeamByID(ctx, lotphaseTeamID)
        if err != nil {
            return err
        }

        if team.Quota.Current > team.Quota.Max {
            return errors.NewErrResourceExhausted(errors.WithMessage("team quota exceeded"))
        }

        return nil
    })
}
```

### 5. **总结**

**团队隔离是完全保障的，不会出现跨团队问题：**

1. **权限隔离**：每个团队只能访问分配给自己的资源
2. **数据隔离**：数据库设计确保团队数据不会混淆
3. **业务隔离**：任务分配、配额管理都是按团队独立进行
4. **接口隔离**：API层面有严格的权限验证
5. **可见性隔离**：团队成员只能看到自己团队的任务

**多团队支持只是在同一阶段允许多个团队并行工作，但各团队之间仍然是完全隔离的。**

## 十一、监控和运维

### 1. 关键指标监控
- 各团队任务分配比例
- 配额使用率
- 任务分配延迟
- 团队间负载均衡度

### 2. 告警设置
- 团队配额超限告警
- 任务分配失败告警
- 负载不均衡告警

### 3. 运维工具
- 团队配额调整工具
- 任务重新分配工具
- 负载均衡调整工具
