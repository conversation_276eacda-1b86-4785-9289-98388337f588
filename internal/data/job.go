// source: anno/v1/job.proto
package data

import (
	"bytes"
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"io"
	"time"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/upload"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

var (
	jobFldID          = biz.JobTableName + ".id"
	jobFldState       = biz.JobTableName + "." + biz.JobSfldState
	jobFldLotID       = biz.JobTableName + "." + biz.JobSfldLotID
	jobFldPhase       = biz.JobTableName + "." + biz.JobSfldPhase
	jobFldCause       = biz.JobTableName + "." + biz.JobSfldCause
	jobFldSubtype     = biz.JobTableName + "." + biz.JobSfldSubtype
	jobFldUpdatedAt   = biz.JobTableName + "." + biz.JobSfldUpdatedAt
	jobFldExecutorUid = biz.JobTableName + "." + biz.JobSfldExecutorUid

	jobFldLastExecteam = biz.JobTableName + "." + biz.JobSfldLastExecteam
)

type jobsRepo struct {
	data *Data
	log  *log.Helper
}

func NewJobsRepo(data *Data, logger log.Logger) biz.JobsRepo {
	return &jobsRepo{data: data, log: log.NewHelper(logger)}
}

func (o *jobsRepo) DoTx(ctx context.Context, fn func(ctx context.Context, tx *gorm.DB) error) (err error) {
	return o.data.DoTx(ctx, fn)
}

func (o *jobsRepo) Create(ctx context.Context, p *biz.Job) (job *biz.Job, err error) {
	err = o.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		job, err = Create(ctx, o.data, p)
		if err != nil {
			fmt.Println("anno-02-create job err:", err)
			return err
		}
		if p.Ally == nil {
			p.Ally = &biz.Jobally{}
		}
		p.Ally.ID = job.ID
		_, err = Create(ctx, o.data, p.Ally)
		if err != nil {
			fmt.Println("anno job create err:", err)
			return fmt.Errorf("failed to create job ally: %w", err)
		}
		return nil
	})
	return
}

func (o *jobsRepo) Update(ctx context.Context, p *biz.Job, fldMask *biz.FieldMask) (*biz.Job, error) {
	allyFlds := make([]string, 0, 3)
	for _, fld := range biz.JoballyUpdatableFlds.Snakecase() {
		if fldMask.HasField(fld) {
			allyFlds = append(allyFlds, fld)
			fldMask.Delete(fld)
		}
	}

	if p.ID == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("job_id"))
	}

	var err error
	err = o.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		if len(allyFlds) > 0 {
			p.Ally.ID = p.ID
			p.Ally, err = Update(ctx, o.data, p.Ally, biz.JoballyUpdatableFlds, field.NewMask(allyFlds...))
		}
		if err == nil && !fldMask.IsEmpty() {
			p, err = Update(ctx, o.data, p, biz.JobUpdatableFlds, fldMask)
		}
		return err
	})
	if err != nil {
		return nil, err
	}
	return p, nil
}

func (o *jobsRepo) LoadPhase(ctx context.Context, pid biz.PhaseID) (*biz.Lotphase, error) {
	phase := &biz.Lotphase{}
	q := o.data.WithCtx(ctx).
		Where(biz.LotphaseSfldLotID.WithTable()+" = ?", pid.LotID).
		Where(biz.LotphaseSfldNumber.WithTable()+" = ?", pid.Number).
		Where(biz.LotphaseSfldExecteam.WithTable()+" IN (?)", []string{pid.Execteam, ""}).
		Where(biz.LotphaseSfldSubtype.WithTable()+" = ?", pid.Subtype).
		Order(biz.LotphaseSfldExecteam.WithTable() + " DESC")
	if err := q.Take(phase).Error; err != nil {
		return nil, Convert(phase, err)
	}
	return phase, nil
}

func (o *jobsRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Job) error {
	ally, err := o.GetJoballyByID(ctx, mod.ID)
	if err != nil {
		return fmt.Errorf("failed to query Jobally: %w", err)
	}

	mod.Ally = ally
	phase, err := o.LoadPhase(ctx, mod.PhaseID())
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to query Lotphase: %w", err)
	}
	mod.LotPhase = phase
	lot, err := GetByID[biz.Lot](ctx, data, mod.LotID)
	if err != nil {
		return fmt.Errorf("failed to query Lot: %w", err)
	}
	mod.Lot = lot
	return err
}

func (o *jobsRepo) GetByID(ctx context.Context, id int64, withAlly bool) (*biz.Job, error) {
	if withAlly {
		return GetByID[biz.Job](ctx, o.data, id, o.loadAssociations)
	}
	return GetByID[biz.Job](ctx, o.data, id)
}

func (o *jobsRepo) GetJoballyByID(ctx context.Context, id int64) (*biz.Jobally, error) {
	return GetByID[biz.Jobally](ctx, o.data, id)
}

func (o *jobsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Job{ID: id})
}

func (o *jobsRepo) buildListQuery(ctx context.Context, p *biz.JobListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.IDs) > 0 {
		q = q.Where("id IN ?", p.IDs)
	}
	if p.LotID > 0 {
		q = q.Where(biz.JobSfldLotID+" = ?", p.LotID)
	}
	if len(p.Phases) > 0 {
		q = q.Where(biz.JobSfldPhase+" IN ?", p.Phases)
	}
	if len(p.States) > 0 {
		q = q.Where(biz.JobSfldState+" IN ?", p.States)
	}
	if len(p.Subtypes) > 0 {
		q = q.Where(biz.JobSfldSubtype+" IN ?", p.Subtypes)
	}
	if p.LastExecteam != "" {
		q = q.Where(biz.JobSfldLastExecteam+" = ?", p.LastExecteam)
	}
	if len(p.LastExecutors) > 0 {
		q = q.Where(biz.JobSfldLastExecutor+" IN ?", p.LastExecutors)
	}
	return q
}

func (o *jobsRepo) List(ctx context.Context, p *biz.JobListFilter, pager biz.Pager) ([]*biz.Job, error) {
	datas := []*biz.Job{}
	q := o.buildListQuery(ctx, p)
	q = q.Order("id")
	if !pager.IsEmpty() {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Job{}, err)
	}
	if len(datas) == 0 {
		return datas, nil
	}

	// load allies
	var allies []*biz.Jobally
	ids := lo.Map(datas, func(v *biz.Job, _ int) int64 { return v.ID })
	jobsMap := lo.SliceToMap(datas, func(v *biz.Job) (int64, *biz.Job) { return v.ID, v })
	err = o.data.WithCtx(ctx).Where("id IN ?", ids).Find(&allies).Error
	if err != nil {
		return nil, Convert(&biz.Jobally{}, err)
	}
	amap := lo.SliceToMap(allies, func(v *biz.Jobally) (int64, *biz.Jobally) { return v.ID, v })
	lo.ForEach(datas, func(v *biz.Job, _ int) { v.Ally = amap[v.ID] })
	if !p.FullJob {
		return datas, nil
	}

	// expand elements info and annos
	for _, ally := range allies {
		if err := ally.DownloadElements(ctx); err != nil {
			return nil, err
		}
		if err := ally.DownloadAnnotations(ctx, jobsMap[ally.ID].InsCnt, jobsMap[ally.ID].IdxInLot); err != nil {
			return nil, err
		}
		ally.PatchAnnos()
	}

	return datas, nil
}

func (o *jobsRepo) Count(ctx context.Context, p *biz.JobListFilter) (int, error) {
	mod := &biz.Job{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, Convert(mod, err)
	}
	return int(cnt), nil
}

func (o *jobsRepo) CountIns(ctx context.Context, p *biz.JobListFilter) (cnt, total int, err error) {
	mod := &biz.Job{}
	q := o.buildListQuery(ctx, p)
	r := &struct {
		Cnt   int
		Total int
	}{}
	err = q.Model(mod).Select(fmt.Sprintf("sum(%v) AS cnt, sum(%v) AS total",
		biz.JobSfldInsCnt, biz.JobSfldInsTotal)).Scan(&r).Error
	if err != nil {
		return 0, 0, Convert(mod, err)
	}
	return r.Cnt, r.Total, nil
}

func (o *jobsRepo) HasHoldingJobs(ctx context.Context, userUids []string) ([]string, error) {
	mod := &biz.Job{}
	var data []string
	err := o.data.WithCtx(ctx).Model(mod).
		Select(biz.JobSfldExecutorUid).
		Joins(fmt.Sprintf("JOIN %s ON %s = %s", biz.LotTableName, biz.JobTableName+"."+biz.JobSfldLotID, biz.LotSfldID.WithTable())).
		Where(biz.JobSfldExecutorUid+" IN ?", userUids).
		Where(biz.LotSfldState.WithTable()+" IN ?", []biz.LotState{biz.LotStateOngoing, biz.LotStatePaused}).
		Group(biz.JobSfldExecutorUid).
		Scan(&data).Error
	if err != nil {
		return nil, Convert(mod, err)
	}
	return data, nil
}

func (o *jobsRepo) GetAnnos(ctx context.Context, p *biz.JobListFilter, pager biz.Pager) (
	[]*biz.Jobally, string, error) {
	// // wrap it in a struct to workaround the error: "unsupported data type: &[]"
	// type Anno struct{ Annotations biz.Annotations }
	// annos := []Anno{}

	subq := o.buildListQuery(ctx, p)
	subq = subq.Model(&biz.Job{}).Select("id")
	q := o.data.WithCtx(ctx).Model(&biz.Jobally{}).Select(field.Join("id", biz.JoballySfldAnnotations.String())).
		Where("id IN (?)", subq).Order("id").Limit(pager.Pagesz)
	if pager.PageToken != "" {
		q = q.Where("id > ?", kid.ParseID(pager.PageToken.String()))
	}

	var annos []*biz.Jobally
	err := q.Scan(&annos).Error
	if err != nil {
		return nil, "", Convert(&biz.Jobally{}, err)
	}
	nextPageToken := ""
	if len(annos) == pager.Pagesz {
		nextPageToken = kid.StringID(annos[len(annos)-1].ID)
	}
	return annos, nextPageToken, nil
}

func (o *jobsRepo) Summary(ctx context.Context, p *biz.JobListFilter) (phases []int, err error) {
	fldPhase := biz.JobSfldPhase
	// fldState := biz.JobSfldState

	// // count finished jobs
	// err = o.buildListQuery(ctx, p).Model(&biz.Job{}).
	// 	Select("count(*)").
	// 	Where(fldState+" = ?", biz.JobStateFinished).
	// 	Scan(&finished).Error
	// if err != nil {
	// 	return nil, 0, err
	// }

	// count jobs at each phase; last_phase+1 means finished jobs
	var sum []struct {
		Phase int
		Count int
	}
	q := o.buildListQuery(ctx, p)

	err = q.Model(&biz.Job{}).
		Select(fldPhase + ", count(*) AS count").
		// Where(fldState+" <> ?", biz.JobStateFinished).
		Group(fldPhase).Order(fldPhase).
		Scan(&sum).Error
	if err != nil {
		return nil, Convert(&biz.Job{}, err)
	}
	if sum == nil {
		return
	}
	phases = make([]int, sum[len(sum)-1].Phase)
	for _, e := range sum {
		phases[e.Phase-1] = e.Count
	}
	return
}

func (o *jobsRepo) GetLotLastJob(ctx context.Context, lotID int64) (*biz.Job, error) {
	j := &biz.Job{}
	err := o.data.WithCtx(ctx).Last(j, biz.JobSfldLotID+" = ?", lotID).Error
	if err != nil {
		return nil, Convert(j, err)
	}
	err = o.loadAssociations(ctx, o.data, j)
	if err != nil {
		return nil, err
	}
	return j, nil
}

func (o *jobsRepo) GetAssignedJob(ctx context.Context, lotID int64, subtype, executorUid string,
	prefer biz.ClaimJobPrefer) (*biz.Job, error) {
	fldLotsState := biz.LotSfldState.WithTable()

	job := &biz.Job{}
	q := o.data.WithCtx(ctx).Model(job). // Select("jobs.*").
						Joins("JOIN lots ON "+jobFldLotID+" = lots.id").
						Where(fldLotsState+" = ?", biz.LotStateOngoing).
						Where(jobFldState+" IN ?", []int32{biz.JobStateUnstart.Value(), biz.JobStateDoing.Value()}).
						Where(jobFldExecutorUid+" = ?", executorUid)
	if subtype != "" {
		q = q.Where(jobFldSubtype+" = ?", subtype)
	}
	if lotID > 0 {
		q = q.Where(jobFldLotID+" = ?", lotID)
	}

	if prefer == biz.ClaimJobPreferRejectedFirst { // query if there is a rejected job
		q = q.Session(&gorm.Session{Context: ctx}) // create a safe session to reuse
		if err := q.Where(jobFldCause+" IN (?)", []string{biz.JobActionReject, biz.JobActionForceReject}).
			Order("jobs.id DESC").Limit(1).
			Scan(job).Error; err != nil {
			return nil, err
		}
		if job.ID != 0 {
			return job, nil
		}
	}

	err := q.Order("jobs.id DESC").Limit(1).Scan(job).Error
	if err != nil || job.ID == 0 {
		return nil, Convert(job, err)
	}
	return job, nil
}

func (o *jobsRepo) GetJobsToAssign(ctx context.Context, lotID int64, subtype, executorUid string) ([]*biz.Job, error) {
	fldExecLotID := biz.LotexecutorSfldLotID
	fldExecSubtype := biz.LotexecutorSfldSubtype

	var lotphase biz.Lotphase
	if err := o.data.WithCtx(ctx).
		Joins(fmt.Sprintf("JOIN %s ON %s = %s AND %s = %s AND %s = %s AND %s = %s", biz.Lotexecutor{}.TableName(),
			biz.LotphaseSfldLotID.WithTable(), biz.LotexecutorSfldLotID.WithTable(),
			biz.LotphaseSfldNumber.WithTable(), biz.LotexecutorSfldPhase.WithTable(),
			biz.LotphaseSfldExecteam.WithTable(), biz.LotexecutorSfldTeamUid.WithTable(),
			biz.LotphaseSfldSubtype.WithTable(), biz.LotexecutorSfldSubtype.WithTable(),
		)).
		Where(biz.LotphaseSfldLotID.WithTable()+" = ?", lotID).
		Where(biz.LotphaseSfldSubtype.WithTable()+" = ?", subtype).
		Where(biz.LotexecutorSfldUserUid.WithTable()+" = ?", executorUid).
		First(&lotphase).Error; err != nil {
		return nil, err
	}

	jobs := []*biz.Job{}
	subq := o.data.WithCtx(ctx).Model(&biz.Lotexecutor{}).
		Select(field.Join(fldExecLotID.String(), biz.LotexecutorSfldPhase.String(), fldExecSubtype.String())).
		Joins("JOIN lots ON "+fldExecLotID.WithTable()+" = lots.id").
		Where(biz.LotSfldState.WithTable()+" = ?", biz.LotStateOngoing).
		// Avoid posting joblogs to Annostat (by job claims) before JobReady
		// because there is no jobs in Annostat at that time.
		Where(biz.LotSfldJobReady.WithTable()).
		Where(biz.LotexecutorSfldUserUid.WithTable()+" = ?", executorUid)
	if subtype != "" {
		subq = subq.Where(fldExecSubtype.WithTable()+" = ?", subtype)
	}
	if lotID > 0 {
		subq = subq.Where(fldExecLotID.WithTable()+" = ?", lotID)
	}

	// no need to order by phase because a user is assigned to only a phase
	q := o.data.WithCtx(ctx).Model(jobs).
		Where(biz.JobSfldExecutorUid+" = '' AND "+biz.JobSfldState+" = ?", biz.JobStateUnstart).
		Where("("+field.Join(biz.JobSfldLotID, biz.JobSfldPhase, biz.JobSfldSubtype)+") IN (?)", subq)
	if lotphase.Number > 1 {
		switch lotphase.ClaimPolicy {
		case anno.Lotphase_ClaimPolicy_only_same_team.String():
			q = q.Where(jobFldLastExecteam+" = ?", lotphase.Execteam)
		case anno.Lotphase_ClaimPolicy_only_other_teams.String():
			q = q.Where(jobFldLastExecteam+" != ?", lotphase.Execteam)
		}
	}
	err := q.Order("id").Limit(10).Find(&jobs).Error
	if err != nil {
		return nil, Convert(&biz.Job{}, err)
	}
	return jobs, nil
}

// SnatchJobs tries to snatch timeout jobs from others.
func (o *jobsRepo) SnatchJobs(ctx context.Context, lotID int64, subtype, executorUid string) ([]*biz.Job, error) {
	// find candidate jobs and order by their udpate time:
	// 1. jobs doing by someone but timeout
	db := o.data.WithCtx(ctx)
	q := db.
		Joins("JOIN "+biz.LotexecutorTableName+" ON "+execFldLotID+" = "+jobFldLotID+" AND "+
			execFldPhase+" = "+jobFldPhase+" AND "+execFldSubtype+" = "+jobFldSubtype).
		Joins("JOIN "+biz.LotphaseTableName+" ON "+phaseFldLotID+" = "+jobFldLotID+" AND "+
			phaseFldNumber+" = "+jobFldPhase+" AND "+phaseFldSubtype+" = "+jobFldSubtype).
		Joins("JOIN "+biz.LotTableName+" ON "+biz.LotSfldID.WithTable()+" = "+jobFldLotID).
		Where(biz.LotSfldState.WithTable()+" = ?", biz.LotStateOngoing).
		// Avoid posting joblogs to Annostat (by job claims) before JobReady
		// because there is no jobs in Annostat at that time.
		Where(biz.LotSfldJobReady.WithTable()).
		Where(biz.LotphaseSfldExecteam.WithTable()+" = ''").                             // select one of the multiple records of a phase
		Where(biz.LotexecutorSfldUserUid.WithTable()+" = ?", executorUid).               // ensure the user is assigned to this lot phase
		Where(jobFldExecutorUid+" != ''").Where(jobFldExecutorUid+" <> ?", executorUid). // assigned to/claimed by others
		Where(db.
			Or( // claimed but timeout
				db.Where(jobFldState+" = ?", biz.JobStateDoing).
					Where(jobFldUpdatedAt + " < NOW() - CONCAT(" + biz.LotphaseSfldTimeout.WithTable() + ", 'seconds')::interval")))
	if subtype != "" {
		q = q.Where(jobFldSubtype+" = ?", subtype)
	}
	if lotID > 0 {
		q = q.Where(jobFldLotID+" = ?", lotID)
	}
	jobs := []*biz.Job{}
	err := q.Order(jobFldUpdatedAt).Limit(10).Find(&jobs).Error
	if err != nil {
		return nil, Convert(&biz.Job{}, err)
	}
	return jobs, nil
}

func (o *jobsRepo) CheckRejectedJobs(ctx context.Context, executorUid string, lotIDs []int64) ([]int64, error) {
	var rejectedLots []int64

	q := o.data.WithCtx(ctx).Model(&biz.Job{}).
		Where(biz.JobSfldCause+" IN (?)", []string{biz.JobActionReject, biz.JobActionForceReject}).
		Where(biz.JobSfldExecutorUid+" = ?", executorUid)
	if len(lotIDs) > 0 {
		q = q.Where(biz.JobSfldLotID+" IN (?)", lotIDs)
	}

	if err := q.Select(biz.JobSfldLotID).Find(&rejectedLots).Error; err != nil {
		return nil, Convert(&biz.Job{}, err)
	}

	return rejectedLots, nil
}

func (o *jobsRepo) ClearExecutor(ctx context.Context, lotID int64, phase int32, execteam string) error {
	const jobsPrefix = biz.JobTableName + "."
	joins := "JOIN " + biz.LotexecutorTableName +
		" ON " + jobsPrefix + biz.JobSfldLotID + " = " + biz.LotexecutorSfldLotID.WithTable() +
		" AND " + jobsPrefix + biz.JobSfldPhase + " = " + biz.LotexecutorSfldPhase.WithTable() +
		" AND " + jobsPrefix + biz.JobSfldExecutorUid + " = " + biz.LotexecutorSfldUserUid.WithTable()
	cond := map[string]any{
		jobsPrefix + biz.JobSfldLotID:          lotID,
		jobsPrefix + biz.JobSfldPhase:          phase,
		biz.LotexecutorSfldTeamUid.WithTable(): execteam,
		jobsPrefix + biz.JobSfldState:          biz.JobStateUnstart,
	}
	// clear executor_uid for these jobs so that they can be claimed again
	err := o.data.WithCtx(ctx).Model(&biz.Job{}).
		Where(jobsPrefix+"id IN (?)",
			o.data.WithCtx(ctx).Model(&biz.Job{}).
				Joins(joins).
				Where(cond).
				Select("DISTINCT "+jobsPrefix+"id"),
		).
		Updates(map[string]any{biz.JobSfldExecutorUid: ""}).Error
	return Convert(&biz.Job{}, err)
}

func (o *jobsRepo) CreateJoblog(ctx context.Context, p *biz.Joblog) (jlog *biz.Joblog, err error) {
	jlog, err = Create(ctx, o.data, p)
	if err != nil {
		return
	}
	err = biz.PublishJoblog(ctx, jlog)
	if err != nil {
		return nil, fmt.Errorf("failed to publish joblog event: %w", err)
	}
	return
}

func (o *jobsRepo) UpdateJoblog(ctx context.Context, p *biz.Joblog, fldMask *biz.FieldMask) (*biz.Joblog, error) {
	return Update(ctx, o.data, p, biz.JoblogUpdatableFlds, fldMask)
}

func (o *jobsRepo) GetJoblog(ctx context.Context, id int64) (*biz.Joblog, error) {
	return GetByID[biz.Joblog](ctx, o.data, id)
}

func (o *jobsRepo) ListJoblog(ctx context.Context, filter biz.ListFilter, pager biz.Pager) ([]*biz.Joblog, error) {
	datas := []*biz.Joblog{}
	q := o.data.WithCtx(ctx)
	q = filter.Apply(q)
	err := q.Order("id DESC").
		Offset(pager.Offset()).Limit(pager.Pagesz).
		Find(&datas).Error
	return datas, Convert(&biz.Joblog{}, err)
}

func (o *jobsRepo) CountJoblog(ctx context.Context, filter biz.ListFilter) (int, error) {
	var cnt int64
	q := o.data.WithCtx(ctx)
	q = filter.Apply(q)
	err := q.Model(&biz.Joblog{}).Count(&cnt).Error
	return int(cnt), Convert(&biz.Joblog{}, err)
}

// GetPhaseLastExecutor gets the previous executor in the phase `phase`.
func (o *jobsRepo) GetPhaseLastExecutor(ctx context.Context, jobID int64, phase int) (user, team string, err error) {
	data := &biz.Joblog{}
	q := o.data.WithCtx(ctx).
		Where(biz.JoblogSfldJobID+" = ?", jobID).
		Where(biz.JoblogSfldToPhase+" = ?", phase).
		Where(biz.JoblogSfldToExecutorUid + " <> ''")
	err = q.Last(data).Error
	if err != nil {
		return "", "", Convert(data, err)
	}
	return data.ToExecutorUid, data.ToExecteam, nil
}

// GetToPhaseLastExecutor gets the last executor that moves the job to the phase `toPhase`.
func (o *jobsRepo) GetToPhaseLastExecutor(ctx context.Context, jobID int64, toPhase int) (user, team string, err error) {
	data := &biz.Joblog{}
	q := o.data.WithCtx(ctx).
		Where(biz.JoblogSfldJobID+" = ?", jobID).
		Where(biz.JoblogSfldToPhase+" = ?", toPhase).
		Where(biz.JoblogSfldAction+" IN (?)", biz.JobRealSubmitActions)
	err = q.Last(data).Error
	if err != nil {
		return "", "", Convert(data, err)
	}
	return data.OperatorUid, data.OpOrgUid, nil
}

func (o *jobsRepo) GetPhaseExecutors(ctx context.Context, jobIDs []int64, maxPhase int32) ([]*biz.Joblog, error) {
	var data = []*biz.Joblog{}
	q := o.data.WithCtx(ctx).
		Select(field.Join(biz.JoblogSfldJobID, biz.JoblogSfldToPhase, biz.JoblogSfldToExecutorUid, biz.JoblogSfldToExecteam, biz.JoblogSfldCreatedAt)).
		Where(biz.JoblogSfldJobID+" IN (?)", jobIDs).
		Where(biz.JoblogSfldAction+" = ?", biz.JobActionClaim).
		Where(biz.JoblogSfldToPhase+" <= ?", maxPhase).
		Where(biz.JoblogSfldToExecutorUid + " <> ''").
		Order("id")
	if err := q.Find(&data).Error; err != nil {
		return nil, Convert(&biz.Joblog{}, err)
	}
	return data, nil
}

func (o *jobsRepo) GetExecutorTeamsFromLog(ctx context.Context, jobIDs []int64, executors ...string) (
	executorToTeam map[string]string, err error) {
	data := []*biz.Joblog{}
	q := o.data.WithCtx(ctx).
		Where(biz.JoblogSfldJobID+" IN ?", jobIDs).
		Where(biz.JoblogSfldToExecutorUid+" IN ?", executors)
	err = q.Select(field.Join(biz.JoblogSfldToExecutorUid, biz.JoblogSfldToExecteam)).Find(&data).Error
	if err != nil {
		return nil, Convert(data, err)
	}
	return lo.SliceToMap(data, func(v *biz.Joblog) (string, string) { return v.ToExecutorUid, v.ToExecteam }), nil
}

// ChangeJobState includes multiple updates, and should be called in a tx.
func (o *jobsRepo) ChangeJobState(ctx context.Context, job *biz.Job, p *biz.JobAction) (bool, error) {
	val := map[string]any{
		biz.JobSfldPhase:       p.NewState.Phase,
		biz.JobSfldState:       p.NewState.State,
		biz.JobSfldCause:       p.NewState.Cause,
		biz.JobSfldExecutorUid: p.NewState.ExecutorUid,
	}
	if p.NewState.InsCnt.Valid {
		val[biz.JobSfldInsCnt] = p.NewState.InsCnt.Value
	}
	if p.NewState.InsTotal.Valid {
		val[biz.JobSfldInsTotal] = p.NewState.InsTotal.Value
	}

	switch p.Action {
	case biz.JobActionClaim:
		if uid := p.NewState.ExecutorUid; uid != "" {
			val[biz.JobSfldLastExecutor] = uid
			val[biz.JobSfldLastExecteam] = p.NewState.Execteam
		}
	}
	spew.Dump("change job state - update jobs val: ", val)
	spew.Dump(ctx)
	r := o.data.WithCtx(ctx).Model(job).Where(map[string]any{
		biz.JobSfldPhase:       job.Phase,
		biz.JobSfldState:       job.State,
		biz.JobSfldExecutorUid: job.ExecutorUid,
	}).Updates(val)
	success, err := r.RowsAffected > 0, r.Error
	fmt.Println("---> update job jobs err: ", r.RowsAffected, err)
	if !success || !(p.Annotations.Valid || p.Comments.Valid) {
		return success, Convert(job, err)
	}

	// update ally items
	flds := map[string]any{}
	fmt.Println("---> update job ally flds: ", flds)
	fmt.Println("---> job annotation valid: ", p.Annotations.Valid, biz.JoballySfldAnnotations.String())
	if p.Annotations.Valid {
		flds[biz.JoballySfldAnnotations.String()] = p.Annotations.Value
	}
	if p.Comments.Valid {
		flds[biz.JoballySfldComments.String()] = p.Comments.Value
	}
	if p.AnnotationsURI.Valid {
		flds[biz.JoballySfldAnnotationsURI.String()] = p.AnnotationsURI.Value
	}
	if p.CommentsURI.Valid {
		flds[biz.JoballySfldCommentsURI.String()] = p.CommentsURI.Value
	}
	fmt.Println("---> lots====, len: ", len(flds))
	ally := &biz.Jobally{ID: job.ID}
	err = o.data.WithCtx(ctx).Model(ally).Updates(flds).Error
	fmt.Println("---> update state ally err===, err: ", err)
	if err == nil {
		job.Ally = ally
	} else {
		success = false
		err = fmt.Errorf("failed to udpate job ally: %w", Convert(ally, err))
	}
	return success, err
}

func (o *jobsRepo) BatchUpdate(ctx context.Context, mod any, updates map[string]any, ids []int64) error {
	err := o.data.WithCtx(ctx).Model(mod).Where("id IN ?", ids).Updates(updates).Error
	return Convert(mod, err)
}

func (o *jobsRepo) BatchCreateJoblog(ctx context.Context, logs []*biz.Joblog) ([]*biz.Joblog, error) {
	return BatchCreate(ctx, o.data, logs)
}

func (o *jobsRepo) GetJobExecutorTeam(ctx context.Context, jobID int64, executor string) (execteam string, err error) {
	err = o.data.WithCtx(ctx).Model(&biz.Lotexecutor{}).
		Joins(fmt.Sprintf("JOIN jobs ON jobs.%v=%s", biz.JobSfldLotID, biz.LotexecutorSfldLotID.WithTable())).
		Where("jobs.id = ?", jobID).
		Where(biz.LotexecutorSfldUserUid.WithTable()+" = ?", executor).
		Select(biz.LotexecutorSfldTeamUid.WithTable()).
		First(&execteam).Error
	err = Convert(nil, err)
	return
}

// getLotByJobID gets a lot by job ID. Note: only id and orgUid fields are queried.
func (o *jobsRepo) getLotByJobID(ctx context.Context, jobID int64) (*biz.Lot, error) {
	var lot = &biz.Lot{}
	err := o.data.WithCtx(ctx).Model(&biz.Lot{}).
		Select(biz.LotSfldID.WithTable(), biz.LotSfldOrgUid.WithTable()).
		Joins(fmt.Sprintf("JOIN %s ON %s=%s", biz.JobTableName, biz.LotSfldID.WithTable(), jobFldLotID)).
		Where(jobFldID+"= ?", jobID).
		First(lot).Error
	if err != nil {
		return nil, err
	}
	return lot, nil
}

func (o *jobsRepo) SaveJobDraft(ctx context.Context, jobID int64, draft *biz.JobDraft) error {
	t1 := time.Now()
	t := time.Now()
	fmt.Println("---> begin save job draft: ", t1.String())
	if draft != nil && len(draft.Content) > 0 && !biz.ShouldSaveBigValuesInDB() {
		lot, err := o.getLotByJobID(ctx, jobID)
		if err != nil {
			return err
		}
		fmt.Println("---> end get lot by job id: ", lot.ID, time.Now().Sub(t))
		t = time.Now()

		dummyJob := &biz.Job{ID: jobID}
		fname := "draft.json"
		key := dummyJob.BuildUploadKey(fname, lot.OrgUid, lot.GetUid())
		var reader io.Reader
		// 判断是否带有3d分割数据
		//var draftContent struct {
		//	Draft string `json:"draft"`
		//}
		//err = json.Unmarshal(draft.Content, &draftContent)
		//if err != nil {
		//	return err
		//}
		//var draftSegInfo struct {
		//	Segmentation3d []*anno.Segmentation3D `json:"segmentation3d"`
		//}
		//err = json.Unmarshal([]byte(draftContent.Draft), &draftSegInfo)
		//if err != nil {
		//	return err
		//}
		//if draftSegInfo.Segmentation3d != nil && len(draftSegInfo.Segmentation3d) > 0 {
		//	if lo.ContainsBy(draftSegInfo.Segmentation3d, func(item *anno.Segmentation3D) bool {
		//		return item != nil
		//	}) {
		if biz.GetSegmentation3dFlag(ctx) {
			spew.Dump("ctx: ", ctx)
			fmt.Println("has segmentation3d !!!")
			zipData, err := biz.CompressGzipData(draft.Content, key)
			if err != nil {
				return fmt.Errorf("compress draft data failed: %w", err)
			}
			key = biz.BuildUploadGzipKey(key)
			reader = bytes.NewReader(zipData)
			//}
		} else {
			fmt.Println("no segmentation3d !!!")
			reader = bytes.NewReader(draft.Content)
		}
		fmt.Println("---> begin upload draft: ", time.Now().String())
		t = time.Now()
		result, err := upload.Upload(ctx, key, reader, &upload.Options{
			Size:    int64(len(draft.Content)),
			Upload2: true,
		})
		if err != nil {
			return err
		}
		fmt.Println("---> end upload draft: ", time.Now().Sub(t), time.Now().String())
		draft.URI = result.URI
		draft.Content = nil
	}

	fmt.Println("---> begin save draft db ", time.Now().String())
	t = time.Now()
	jobally := &biz.Jobally{ID: jobID, Draft: *serial.New(draft)}
	fldMask := field.NewMask(biz.JoballySfldDraft.String())
	_, err := Update[biz.Jobally](ctx, o.data, jobally, biz.JoballyUpdatableFlds, fldMask)
	fmt.Println("---> end save draft db ", time.Now().Sub(t), time.Now().String())

	fmt.Println("end save job draft: ", time.Now().Sub(t1).Seconds(), time.Now().String())
	return err
}

func (o *jobsRepo) GetJobDraft(ctx context.Context, jobID int64) (*biz.JobDraft, error) {
	var jobally biz.Jobally
	err := o.data.WithCtx(ctx).Model(&biz.Jobally{}).
		Select(biz.JoballySfldID.WithTable(), biz.JoballySfldDraft.WithTable()).
		Where(biz.JoballySfldID.WithTable()+" = ?", jobID).
		First(&jobally).Error
	if err != nil {
		return nil, err
	}
	return jobally.Draft.E, nil
}
func (o *jobsRepo) GetJobClips(ctx context.Context, jobIDs []int64) ([]*biz.Jobelem, error) {
	dbselect := fmt.Sprintf("DISTINCT ON(%s) %s ,%s", biz.Jobelem_JobID.Full(), biz.Jobelem_JobID.Full(), biz.Jobelem_ElemName.Full())
	var data = []*biz.Jobelem{}
	err := o.data.WithCtx(ctx).Model(&biz.Jobelem{}).
		Where(biz.Jobelem_JobID.Full()+" IN (?)", jobIDs).
		Select(dbselect).
		Find(&data).Error

	return data, err
}

func (o *jobsRepo) GetJobByClip(ctx context.Context, clip string) (*biz.Jobelem, error) {
	//dbselect := fmt.Sprintf("DISTINCT ON(%s) %s ,%s", biz.Jobelem_JobID.Full(), biz.Jobelem_JobID.Full(), biz.Jobelem_ElemName.Full())
	var data = biz.Jobelem{}
	err := o.data.WithCtx(ctx).Model(&biz.Jobelem{}).
		Where(biz.Jobelem_ElemName.Full()+" like (?)", "%"+clip+"%").
		//Select(dbselect).
		First(&data).Error

	return &data, err
}

func (o *jobsRepo) GetJobsPhaseCountByLotIds(ctx context.Context, lotIds []int64) ([]biz.RespJobsPhaseCountByLotIds, error) {
	mod := &biz.Job{}
	var results []biz.RespJobsPhaseCountByLotIds
	err := o.data.WithCtx(ctx).Model(mod).
		Select("lot_id, COUNT(id) as count, phase").
		Where("lot_id IN ?", lotIds).
		Group("lot_id, phase").
		Scan(&results).Error

	return results, err
}
