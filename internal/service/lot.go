// source: anno/v1/lot.proto
package service

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"anno/api/client"
	"anno/internal/biz"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/types"
	"gitlab.rp.konvery.work/platform/pkg/container/kset"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

func ClearLotPrivacy(d *biz.Lot) {
	d.OrgUid = ""
	d.Desc = ""
	d.DataUid = ""
	d.CreatorUid = ""
	d.OrderID = 0
}

type LotsService struct {
	anno.UnimplementedLotsServer
	bz      *biz.LotsBiz
	repo    biz.LotsRepo
	jobbz   *biz.JobsBiz
	orderbz *biz.OrdersBiz
	bgtask  biz.BackgroundTask
	log     *log.Helper
}

func NewLotsService(logger log.Logger, bz *biz.LotsBiz, repo biz.LotsRepo, jobbz *biz.JobsBiz, orderbz *biz.OrdersBiz,
	bgtask biz.BackgroundTask) *LotsService {
	return &LotsService{log: log.NewHelper(logger), bz: bz, repo: repo, jobbz: jobbz, orderbz: orderbz, bgtask: bgtask}
}

func (o *LotsService) CreateLot(ctx context.Context, req *anno.CreateLotRequest) (*anno.Lot, error) {
	fixReq := func(name, org string) {
		if req.Name == "" {
			req.Name = name
		}
		if req.OrgUid == "" {
			req.OrgUid = org
		}
	}

	orderUid, dataUid := req.OrderUid, req.DataUid
	if orderUid != "" {
		// check order
		// KAM might not have order permissions. Disable the check so that they can create lots.
		// if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsOrder, orderUid) {
		// 	return nil, errors.NewErrForbidden()
		// }
		order, err := o.orderbz.GetByUid(ctx, orderUid)
		if err != nil {
			return nil, err
		}
		if dataUid == "" {
			dataUid = order.DataUid
			req.DataUid = dataUid
		}
		fixReq(order.Name, order.OrgUid)
	}

	var dataSize int32
	var dataTypeEnum anno.Element_Type_Enum
	if dataUid == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("data_uid"))
	} else {
		// check data
		data, err := client.GetData(ctx, dataUid)
		if err != nil {
			return nil, errors.FromError(err)
		}
		if orderUid == "" {
			orderUid = data.OrderUid
			req.OrderUid = orderUid
		}
		dataSize = data.Size
		dataTypeEnum = data.Type
		if req.OrgUid != "" && data.OrgUid != "" && req.OrgUid != data.OrgUid {
			return nil, errors.NewErrConflict(errors.WithMessage("conflicting data and lot org_uid"))
		}
		fixReq(data.Name, data.OrgUid)
	}
	if orderUid != req.OrderUid || dataUid != req.DataUid {
		return nil, errors.NewErrBadRequest(errors.WithMessage("order and data have conflicts"))
	}

	user := biz.UserFromCtx(ctx)
	scope := req.OrgUid
	if scope == "" {
		scope = user.GetOrgUid()
		req.OrgUid = scope
	}
	if scope == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
	}
	scope = client.GroupScope(scope)
	if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsLot, scope) {
		return nil, errors.NewErrForbidden()
	}

	if err := o.checkCreateLotReq(req); err != nil {
		return nil, err
	}
	dataType := dataTypeEnum.String()
	if dataTypeEnum == anno.Element_Type_unspecified {
		dataType = ""
	}

	lot := ToBizLot(req)
	lot.DataSize = dataSize
	lot.CreatorUid = user.GetUid()
	lot.DataType = dataType
	lot, err := o.bz.Create(ctx, lot)
	return FromBizLot(lot), err
}

func (o *LotsService) checkCreateLotReq(req *anno.CreateLotRequest) error {
	if len(req.Phases) == 0 {
		return errors.NewErrEmptyField(errors.WithFields("phases"))
	}
	for i, phase := range req.Phases {
		if err := o.checkExecteamQuotas(phase.Execteams, i+1); err != nil {
			return err
		}
	}

	return nil
}

func (o *LotsService) checkExecteamQuotas(execteamQuotas []*anno.Lotphase_Execteam, phase int) error {
	sumQuotaMin := int32(0)
	seenExecteams := make(map[string]bool)
	for _, quota := range execteamQuotas {
		if quota.Execteam == "" {
			return errors.NewErrEmptyField(
				errors.WithMessage(fmt.Sprintf("execteam cannot be empty in phase-%d", phase)),
				errors.WithFields("execteam"))
		}
		if seenExecteams[quota.Execteam] {
			return errors.NewErrInvalidField(
				errors.WithMessage(fmt.Sprintf("duplicate execteam in phase-%d", phase)),
				errors.WithFields("execteam"))
		}
		seenExecteams[quota.Execteam] = true

		if quota.Quota == nil { // no quota limit
			continue
		}
		if quota.Quota.Min > quota.Quota.Max {
			return errors.NewErrInvalidField(
				errors.WithMessage(fmt.Sprintf(
					"execteam quota min must be less than or equal to execteam quota max in phase-%d", phase,
				)),
				errors.WithFields("quota"))
		}

		sumQuotaMin += quota.Quota.Min
		if sumQuotaMin > 100 {
			return errors.NewErrInvalidField(
				errors.WithMessage(fmt.Sprintf(
					"sum of execteam quota is greater than 100%% in phase-%d", phase,
				)),
				errors.WithFields("quota"))
		}
	}
	return nil
}

func (o *LotsService) UpdateLot(ctx context.Context, req *anno.UpdateLotRequest) (*anno.Lot, error) {
	flds, err := CheckUpdateFlds(biz.Lot{}, req.Fields, biz.LottplSfldName, biz.LotSfldDesc.String(), biz.LotallySfldOntologies)
	if err != nil {
		return nil, err
	}

	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Lot.Uid) {
		return nil, errors.NewErrForbidden()
	}

	if lo.Contains(flds, biz.LotallySfldOntologies) {
		// ensure ontology groups are not updated
		lot, err := o.bz.GetByUid(ctx, req.Lot.Uid)
		if err != nil {
			return nil, err
		}
		old := lo.Map(lot.Ally.Ontologies.E.Groups, func(v *anno.Lotontologies_Group, _ int) string { return v.Name })
		new := lo.Map(req.Lot.Ontologies.Groups, func(v *anno.Lotontologies_Group, _ int) string { return v.Name })
		joins := lo.Intersect(old, new)
		if len(joins) != len(old) || len(joins) != len(new) {
			return nil, errors.NewErrUnsupportedField(errors.WithMessage("cannot udpate ontology groups"))
		}
	}

	updates := ToBizLot(req.Lot)
	// if e := data.Error.E; e != nil && e.Reason != "" {
	// 	// TODO: not all updates should clear the error state
	// 	data.Error.E = nil
	// 	flds = append(flds, biz.LotSfldError)
	// }
	data, err := o.bz.Update(ctx, updates, field.NewMask(flds...))
	return FromBizLot(data), err
}

func (o *LotsService) DeleteLot(ctx context.Context, req *anno.DeleteLotRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermDelete, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if !data.State.IsFinal() && data.State != biz.LotStateUnstart {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("lot is not stopped"))
	}
	if data.State.IsFinal() && time.Since(data.UpdatedAt) < 24*time.Hour {
		// give enough time for the lot worflow to complete
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("too short time since the lot is stopped"))
	}

	// TODO: delete all jobs
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *LotsService) GetLot(ctx context.Context, req *anno.GetLotRequest) (*anno.Lot, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}

	o.signAnnoResult(ctx, data)
	return FromBizLot(data), err
}

func (o *LotsService) ListLot(ctx context.Context, req *anno.ListLotRequest) (*anno.ListLotReply, error) {
	creator, scope := getListScope(ctx, req.OrgUid, req.CreatorUid)
	if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsLot, scope) {
		return nil, errors.NewErrForbidden()
	}

	typ := ""
	if req.Type != anno.Lot_Type_unspecified {
		typ = req.Type.String()
	}
	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.LotListFilter{
		OrgUid:      req.OrgUid,
		CreatorUid:  creator,
		OrderID:     kid.ParseID(req.OrderUid),
		NamePattern: req.NamePattern,
		Type:        typ,
		States: lo.Map(req.States, func(v anno.Lot_State_Enum, _ int) biz.LotState {
			return biz.ToBizLotState(v)
		}),
	}
	op := biz.UserFromCtx(ctx)
	switch op.GetRole() {
	case client.SysRoleKAM:
		filter.BizgranteeUid = op.GetUid()
		filter.CreatorUid = ""
	case client.SysRolePM:
		filter.SpecgranteeUid = op.GetUid()
		filter.CreatorUid = ""
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	var orgs []*client.BaseUser
	if req.WithOrg {
		uids := lo.Map(datas, func(d *biz.Lot, _ int) string { return d.OrgUid })
		teams, err := client.ListTeamsInMap(ctx, uids)
		if err != nil {
			return nil, err
		}
		orgs = lo.Map(datas, func(d *biz.Lot, _ int) *client.BaseUser { return teams[d.OrgUid] })
	}
	o.signAnnoResult(ctx, datas...)
	return &anno.ListLotReply{
		Total: int32(cnt),
		Lots:  kslice.Map(FromBizLot, datas),
		Orgs:  orgs}, err
}

func (o *LotsService) StartLot(ctx context.Context, req *anno.GetLotRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if data.State == biz.LotStateFinished {
		return &emptypb.Empty{}, o.bz.RestartLot(ctx, data)
	}

	return &emptypb.Empty{}, o.bz.StartLot(ctx, data)
}

func (o *LotsService) PauseLot(ctx context.Context, req *anno.GetLotRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, o.bz.PauseLot(ctx, data)
}

func (o *LotsService) CancelLot(ctx context.Context, req *anno.GetLotRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, o.bz.CancelLot(ctx, data)
}

func (o *LotsService) AssignExecteam(ctx context.Context, req *anno.AssignExecteamRequest) (*emptypb.Empty, error) {
	if err := o.checkAssignExecteamReq(req); err != nil {
		return nil, err
	}

	if !client.IsAllowed(ctx, "", biz.PermAssignExecteam, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	err := o.bz.AssignPhaseExecteam(ctx, req)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (o *LotsService) checkAssignExecteamReq(req *anno.AssignExecteamRequest) error {
	if len(req.Phases) == 0 {
		return errors.NewErrEmptyField(errors.WithFields("phases"))
	}

	for _, p := range req.Phases {
		if err := o.checkExecteamQuotas(p.GetExecteams(), int(p.Phase)); err != nil {
			return err
		}
	}
	return nil
}

func (o *LotsService) ManageExecutors(ctx context.Context, req *anno.ManageExecutorsRequest) (*emptypb.Empty, error) {
	if len(req.Phases) > 10 {
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many phases"),
			errors.WithFields("phases"))
	}
	for _, p := range req.Phases {
		if len(p.Add) > 100 || len(p.Delete) > 100 {
			return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many executors in a phase"),
				errors.WithFields("add", "delete"))
		}
	}

	// require exector team manager's permission
	checked := make([]string, 0, len(req.Phases))
	for _, p := range req.Phases {
		if lo.Contains(checked, p.TeamUid) {
			continue
		}
		if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsGroup, p.TeamUid) {
			return nil, errors.NewErrForbidden()
		}
		checked = append(checked, p.TeamUid)
	}
	fmt.Println("---> checked", checked)

	// process all delete requests first
	lotID := kid.ParseID(req.Uid)
	phases := make([]*biz.Lotphase, 0, len(req.Phases))
	for _, p := range req.Phases {
		// ensure execteam matches
		phase, err := o.bz.GetLotphase(ctx, lotID, int(p.Phase), p.TeamUid)
		if err != nil {
			return nil, fmt.Errorf("failed to get phase %d: %w", p.Phase, err)
		}
		if phase == nil {
			return nil, errors.NewErrBadRequest(errors.WithMessage("incorrect executor team"))
		}

		if len(p.Delete) > 0 {
			err = o.bz.DeletePhaseExecutors(ctx, phase, p.TeamUid, p.Delete)
			if err != nil {
				return nil, fmt.Errorf("failed to delete executors of phase %d: %w", p.Phase, err)
			}
		}
		phases = append(phases, phase)
	}
	fmt.Println("phases", phases)

	// process add requests
	for i, p := range req.Phases {
		if len(p.Add) > 0 {
			// TODO: ensure users are from the executor team
			err := o.bz.AddPhaseExecutors(ctx, phases[i], p.TeamUid, p.Add)
			if err != nil {
				return nil, fmt.Errorf("failed to add executors of phase %d: %w", p.Phase, err)
			}
		}
	}

	return &emptypb.Empty{}, nil
}

func (o *LotsService) ListExecutors(ctx context.Context, req *anno.ListExecutorsRequest) (*anno.ListExecutorsReply, error) {
	var phase *biz.Lotphase
	teamUid := req.TeamUid
	if teamUid == "" { // compatible if FE does not pass team_uid before FE supports multiple team in one phase
		phases, err := o.bz.LoadLotphase(ctx, kid.ParseID(req.Uid), int(req.Phase))
		if err != nil {
			return nil, err
		}
		if len(phases) == 0 {
			return nil, errors.NewErrNotFound(errors.WithMessage("phase not found"),
				errors.WithModel("lotphase"))
		}
		phases = lo.Filter(phases, func(p *biz.Lotphase, _ int) bool { return p.Execteam != "" })
		if len(phases) > 1 {
			return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many phases"))
		}
		phase = phases[0]
		teamUid = phase.Execteam
	} else {
		p, err := o.bz.GetLotphase(ctx, kid.ParseID(req.Uid), int(req.Phase), teamUid)
		if err != nil {
			return nil, err
		}
		if p == nil {
			return nil, errors.NewErrNotFound(errors.WithMessage("phase not found"),
				errors.WithModel("lotphase"))
		}
		phase = p
	}
	// require exector team manager's permission
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsGroup, teamUid) {
		return nil, errors.NewErrForbidden()
	}

	var cnt int
	if req.Page == 0 {
		var err error
		cnt, err = o.bz.CountPhaseExecutors(ctx, phase, teamUid)
		if err != nil {
			return nil, err
		}
	}

	uids, err := o.bz.ListPhaseExecutors(ctx, phase, teamUid, biz.Pager{Page: int(req.Page), Pagesz: int(req.Pagesz)})
	if err != nil {
		return nil, err
	}
	if len(uids) == 0 {
		return &anno.ListExecutorsReply{}, nil
	}
	users, err := client.ListUsersInMap(client.NewCtxUseSvcAccount(ctx), uids)
	if err != nil {
		return nil, err
	}
	return &anno.ListExecutorsReply{Total: int32(cnt), Users: lo.Values(users)}, nil
}

func (o *LotsService) GetSummary(ctx context.Context, req *anno.GetLotRequest) (*anno.GetLotSummaryReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermStat, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	lot, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	phases, err := o.jobbz.Summary(ctx, &biz.JobListFilter{LotID: lot.ID})
	if err != nil {
		return nil, err
	}
	if n := lot.PhaseCount + 1 - len(phases); n > 0 {
		phases = append(phases, make([]int, n)...)
	}
	return &anno.GetLotSummaryReply{
		TotalJobs:   lo.SumBy(phases, func(v int) int32 { return int32(v) }),
		JobsAtPhase: lo.Map(phases, func(v int, _ int) int32 { return int32(v) }),
	}, nil
}

func (o *LotsService) GetAnnos(ctx context.Context, req *anno.GetLotRequest) (*anno.GetLotAnnosReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermExportAnno, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	annos, _, err := o.jobbz.GetAnnos(ctx, &biz.JobListFilter{
		LotID:    kid.ParseID(req.Uid),
		States:   []biz.JobState{biz.JobStateFinished},
		Subtypes: []string{""},
	}, biz.Pager{Pagesz: 1000})
	if err != nil {
		return nil, err
	}
	r := make([]*anno.ElementAnno, 0, len(annos))
	for _, e := range annos {
		r = append(r, e.Annotations.E.ElementAnnos...)
	}

	insCnt := lo.Reduce(r, func(v int32, e *anno.ElementAnno, _ int) int32 {
		if e == nil {
			return v
		}
		return v + e.InsCnt
	}, 0)
	return &anno.GetLotAnnosReply{Annotations: r, InsCnt: insCnt}, err
}

func (o *LotsService) signAnnoResult(ctx context.Context, lots ...*biz.Lot) {
	for _, v := range lots {
		if e := v.ExtraData.E; e != nil {
			e.AnnoResultUrl = signAnnoResult(ctx, e.AnnoResultUrl, o.log)
		}
	}
}

func (o *LotsService) ExportLotAnnos(ctx context.Context, req *anno.ExportLotAnnosRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermExportAnno, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	lot, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if err := o.bz.ClearAnnoResult(ctx, lot); err != nil {
		return nil, err
	}
	if err := o.orderbz.ClearAnnoResult(ctx, lot.OrderID); err != nil {
		return nil, err
	}

	if _, err := client.ExportLotAnnos(ctx, &client.ExportLotAnnosRequest{
		Uid:    req.Uid,
		Option: req.Option,
		Phases: req.Phases,
	}); err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (o *LotsService) SetLotAnnoResult(ctx context.Context, req *anno.SetLotAnnoResultRequest) (*emptypb.Empty, error) {
	if !client.IsPrivilegedUser(biz.UserFromCtx(ctx).User) &&
		!client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	// Note: the following logic is not concurrent safe, but I feel it can be accepted.
	lotID := kid.ParseID(req.Uid)
	lotExtraData, err := o.bz.GetExtraDataByID(ctx, lotID)
	if err != nil {
		return nil, err
	}
	if lotExtraData == nil {
		lotExtraData = &biz.LotExtraData{}
	}
	lotExtraData.AnnoResultUrl = req.Url

	if _, err := o.bz.Update(ctx,
		&biz.Lot{
			ID:        lotID,
			ExtraData: *serial.New(lotExtraData),
		},
		field.NewMask(biz.LotSfldExtraData.String()),
	); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (o *LotsService) AllowDownloadAnnos(ctx context.Context, req *anno.AllowDownloadAnnosRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	lotID := kid.ParseID(req.Uid)
	lot, err := o.bz.GetByID(ctx, lotID, false)
	if err != nil {
		return nil, err
	}
	if lot.CanExportAnnos == req.Allow {
		return &emptypb.Empty{}, nil
	}

	if err := o.bz.Repo().DoTx(ctx, func(ctx context.Context, tx biz.Tx) error {
		lot.CanExportAnnos = req.Allow
		if _, err := o.bz.Update(ctx, lot, field.NewMask(biz.LotSfldCanExportAnnos.String())); err != nil {
			return err
		}

		if _, err := o.orderbz.Update(ctx,
			&biz.Order{
				ID:             lot.OrderID,
				CanExportAnnos: req.Allow,
			},
			field.NewMask(biz.OrderSfldAnnoResultURL.String(), biz.OrderSfldCanExportAnnos.String()),
		); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (o *LotsService) CloneLot(ctx context.Context, req *anno.CloneLotRequest) (*anno.Lot, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsLot, req.Uid) {
		fmt.Println("check is allow")
		//return nil, errors.NewErrForbidden()
	}

	lot, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}

	// - type: clone lot 只能用于同一个类型。
	// - order_uid: order uid 只能随着 data 变动而变动，因为 order 和 data 是一一对应的。
	forbiddenFields := kset.NewSetFrom("type", "order_uid")
	createLotRequest := buildCreateLotRequestFromLot(lot)
	updateExecteam := false
	if updates := req.Updates; updates != nil && len(updates.Fields) > 0 {
		if updates.Lot == nil {
			return nil, errors.NewErrEmptyField(errors.WithFields("updates.lot"))
		}

		for _, fld := range updates.Fields {
			if forbiddenFields.Has(fld) {
				return nil, errors.NewErrUnsupportedField(errors.WithFields(fld))
			}

			switch fld {
			case "name":
				createLotRequest.Name = updates.Lot.Name
			case "desc":
				createLotRequest.Desc = updates.Lot.Desc
			case "project_uid":
				createLotRequest.ProjectUid = updates.Lot.ProjectUid
			case "priority":
				createLotRequest.Priority = updates.Lot.Priority
			case "autostart":
				createLotRequest.Autostart = updates.Lot.Autostart
			case "job_size":
				createLotRequest.JobSize = updates.Lot.JobSize
			case "ontologies":
				createLotRequest.Ontologies = updates.Lot.Ontologies
			case "phases":
				phaseTeamMapList := biz.LotphaseGroupedTeamMap(lot.Phases)
			outer:
				for i, reqPhase := range updates.Lot.Phases {
					phaseNum := i + 1
					if phaseNum >= len(phaseTeamMapList) { // no need to consider newly added phase
						continue
					}

					phaseTeamMap := phaseTeamMapList[phaseNum]
					if len(phaseTeamMap) != len(reqPhase.Execteams)+1 { // phaseTeamMap contains an empty team
						updateExecteam = true
						break
					}

					for _, execteam := range reqPhase.Execteams {
						if _, ok := phaseTeamMap[execteam.Execteam]; !ok {
							updateExecteam = true
							break outer
						}
					}
				}
				createLotRequest.Phases = updates.Lot.Phases
			case "instruction":
				createLotRequest.Instruction = updates.Lot.Instruction
			case "org_uid":
				createLotRequest.OrgUid = updates.Lot.OrgUid
			case "data_uid":
				createLotRequest.DataUid = updates.Lot.DataUid
				createLotRequest.OrderUid = "" // order uid will be set in CreateLot
			case "out":
				createLotRequest.Out = updates.Lot.Out
			case "exp_end_time":
				createLotRequest.ExpEndTime = updates.Lot.ExpEndTime
			case "is_frame_series":
				createLotRequest.IsFrameSeries = updates.Lot.IsFrameSeries
			case "tool_cfg":
				createLotRequest.ToolCfg = updates.Lot.ToolCfg
			default:
				return nil, errors.NewErrInvalidField(errors.WithFields(fld))
			}
		}
	}

	var executors []*biz.Lotexecutor
	if req.CopyExecutors {
		if updateExecteam {
			return nil, errors.NewErrBadRequest(errors.WithMessage("cannot copy executors when changing execteam"))
		}
		executors, err = o.bz.ListExecutorsByLotID(ctx, lot.ID)
		if err != nil {
			return nil, err
		}
	}

	var newLot *anno.Lot
	if err := o.repo.DoTx(ctx, func(ctx context.Context, tx biz.Tx) error {
		newLot, err = o.CreateLot(ctx, createLotRequest)
		if err != nil {
			return err
		}

		if req.CopyExecutors && len(executors) > 0 {
			for i := range executors {
				executors[i].ID = 0
				executors[i].LotID = kid.ParseID(newLot.Uid)
				executors[i].CreatedAt = time.Time{}
			}
			if err := o.bz.AddExecutors(ctx, executors); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return newLot, nil
}

func (o *LotsService) AddTag(ctx context.Context, req *types.TagRequest) (*types.TagList, error) {
	op := biz.UserFromCtx(ctx)
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	if !client.IsPrivilegedUser(op.User) && lo.SomeBy(req.Tags, func(v string) bool { return biz.IsSysTag(strings.ToLower(v)) }) {
		return nil, errors.NewErrForbidden(errors.WithMessage("cannot contains system tags"))
	}

	lot, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if lo.Every(lot.Tags, req.Tags) {
		return &types.TagList{Tags: filterTags(op.User, lot.Tags)}, nil
	}

	lot.Tags = lo.Union(lot.Tags, req.Tags)
	sort.Strings(lot.Tags)
	_, err = o.bz.Update(ctx, lot, field.NewMask(biz.LotSfldTags.String()))
	if err != nil {
		return nil, err
	}
	return &types.TagList{Tags: filterTags(op.User, lot.Tags)}, nil
}

func (o *LotsService) DeleteTag(ctx context.Context, req *types.TagRequest) (*types.TagList, error) {
	op := biz.UserFromCtx(ctx)
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	if !client.IsPrivilegedUser(op.User) && lo.SomeBy(req.Tags, func(v string) bool { return biz.IsSysTag(strings.ToLower(v)) }) {
		return nil, errors.NewErrForbidden(errors.WithMessage("cannot contains system tags"))
	}

	lot, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if lo.None(lot.Tags, req.Tags) {
		return &types.TagList{Tags: filterTags(op.User, lot.Tags)}, nil
	}

	lot.Tags = lo.Without(lot.Tags, req.Tags...)
	_, err = o.bz.Update(ctx, lot, field.NewMask(biz.LotSfldTags.String()))
	if err != nil {
		return nil, err
	}
	return &types.TagList{Tags: filterTags(op.User, lot.Tags)}, nil
}

func filterTags(op *biz.User, tags []string) []string {
	if client.IsPrivilegedUser(op) {
		return tags
	}
	return lo.Filter(tags, func(v string, _ int) bool { return !biz.IsSysTag(v) })
}

func (o *LotsService) JobCountByLots(ctx context.Context, req *anno.JobCountByLotidsRequest) (*anno.JobCountByLotidsReply, error) {

	var ids = []int64{}
	for _, v := range req.Ids {
		if v != "" {
			ids = append(ids, kid.ParseID(v))
		}
	}
	restmp, err := o.jobbz.GetJobsPhaseCountByLotIds(ctx, ids)

	return ConvertToJobCountByLotidsReply(restmp), err
}

func ConvertToJobCountByLotidsReply(data []biz.RespJobsPhaseCountByLotIds) *anno.JobCountByLotidsReply {
	lotMap := make(map[string]*anno.JobCountByLotidsReply_LotInfo)

	for _, item := range data {
		LotIdInt64, _ := strconv.ParseInt(item.LotId, 10, 64)

		LotIdStr := kid.StringID(LotIdInt64)
		if _, exists := lotMap[LotIdStr]; !exists {
			lotMap[LotIdStr] = &anno.JobCountByLotidsReply_LotInfo{
				LotId: LotIdStr,
				Count: []*anno.JobCountByLotidsReply_PhaseCount{},
			}
		}

		phase, _ := strconv.Atoi(item.Phase)
		lotMap[LotIdStr].Count = append(lotMap[LotIdStr].Count, &anno.JobCountByLotidsReply_PhaseCount{
			Phase: int32(phase),
			Count: int32(item.Count),
		})
	}

	reply := &anno.JobCountByLotidsReply{
		Lots: []*anno.JobCountByLotidsReply_LotInfo{},
	}
	for _, lot := range lotMap {
		reply.Lots = append(reply.Lots, lot)
	}

	return reply
}
