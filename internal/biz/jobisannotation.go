// source: anno/v1/jobisannotation.proto
package biz

import (
	"context"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type JobSkipAnnotation struct {
	ID             int64 `json:"id" gorm:"default:null"`
	LotID          int64 `json:"lot_id" gorm:"default:null"`
	SkipAnnotation bool  `json:"skip_annotation"`
}

func (o JobSkipAnnotation) TableName() string { return JobSkipAnnotationTableName }

const JobSkipAnnotationTableName = "jobskipannotation"

type JobSkipAnnotationRepo interface {
	Create(context.Context, *JobSkipAnnotation) (*JobSkipAnnotation, error)
	GetByID(ctx context.Context, id int64) (*JobSkipAnnotation, error)
	DeleteByID(context.Context, int64) error
}

type JobSkipAnnotationRepoBiz struct {
	Repo JobSkipAnnotationRepo
	log  *log.Helper
}

func NewJobSkipAnnotationRepoBiz(repo JobSkipAnnotationRepo, logger log.Logger) *JobSkipAnnotationRepoBiz {
	return &JobSkipAnnotationRepoBiz{Repo: repo, log: log.NewHelper(logger)}
}

func (o *JobSkipAnnotationRepoBiz) Create(ctx context.Context, p *JobSkipAnnotation) (JobSkipAnnotation *JobSkipAnnotation, err error) {
	return o.Repo.Create(ctx, p)
}

func (o *JobSkipAnnotationRepoBiz) GetByID(ctx context.Context, uid int64) (*JobSkipAnnotation, error) {
	return o.Repo.GetByID(ctx, uid)
}

func (o *JobSkipAnnotationRepoBiz) DeleteByID(ctx context.Context, uid int64) error {
	return o.Repo.DeleteByID(ctx, uid)
}
